import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { OrderService } from '../../../services/order';
import { 
  Order, 
  OrderSummary, 
  OrderStatus, 
  OrderFilter,
  LoadingState,
  ApiResponse 
} from '../../../interfaces';

interface OrderStats {
  total: number;
  pending: number;
  confirmed: number;
  shipped: number;
  delivered: number;
  cancelled: number;
}

@Component({
  selector: 'app-admin-orders',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './orders.html',
  styleUrls: ['./orders.css']
})
export class AdminOrdersComponent implements OnInit, OnDestroy {
  orders: OrderSummary[] = [];
  filteredOrders: OrderSummary[] = [];
  selectedOrder: Order | null = null;
  
  loadingState: LoadingState = {
    isLoading: true,
    error: undefined
  };

  orderStats: OrderStats = {
    total: 0,
    pending: 0,
    confirmed: 0,
    shipped: 0,
    delivered: 0,
    cancelled: 0
  };

  // Filter and pagination
  filter: OrderFilter = {
    sortBy: 'date',
    sortOrder: 'desc'
  };
  
  searchTerm = '';
  selectedStatus: OrderStatus | '' = '';
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  // UI states
  showOrderDetails = false;
  showStatusUpdateModal = false;
  newStatus: OrderStatus = 'pending';

  private subscriptions: Subscription[] = [];

  constructor(
    private orderService: OrderService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadOrders();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadOrders(): void {
    this.loadingState.isLoading = true;
    
    const ordersSub = this.orderService.getUserOrders(1, this.filter).subscribe({
      next: (response: ApiResponse<any>) => {
        if (response.success && response.data) {
          this.orders = response.data.orders || [];
          this.calculateStats();
          this.applyFilters();
        }
        this.loadingState.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading orders:', error);
        this.loadingState.isLoading = false;
        this.loadingState.error = 'Erreur lors du chargement des commandes';
      }
    });

    this.subscriptions.push(ordersSub);
  }

  private calculateStats(): void {
    this.orderStats = {
      total: this.orders.length,
      pending: this.orders.filter(o => o.status === 'pending').length,
      confirmed: this.orders.filter(o => o.status === 'confirmed').length,
      shipped: this.orders.filter(o => o.status === 'shipped').length,
      delivered: this.orders.filter(o => o.status === 'delivered').length,
      cancelled: this.orders.filter(o => o.status === 'cancelled').length
    };
  }

  applyFilters(): void {
    let filtered = [...this.orders];

    // Search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(order => 
        order.orderNumber.toLowerCase().includes(term) ||
        order.id.toString().includes(term)
      );
    }

    // Status filter
    if (this.selectedStatus) {
      filtered = filtered.filter(order => order.status === this.selectedStatus);
    }

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (this.filter.sortBy) {
        case 'date':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'amount':
          comparison = a.total - b.total;
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        default:
          comparison = a.id - b.id;
      }

      return this.filter.sortOrder === 'desc' ? -comparison : comparison;
    });

    this.filteredOrders = filtered;
    this.totalPages = Math.ceil(filtered.length / this.itemsPerPage);
    this.currentPage = Math.min(this.currentPage, this.totalPages || 1);
  }

  get paginatedOrders(): OrderSummary[] {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredOrders.slice(start, end);
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onStatusFilterChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onSortChange(sortBy: 'date' | 'amount' | 'status'): void {
    if (this.filter.sortBy === sortBy) {
      this.filter.sortOrder = this.filter.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.filter.sortBy = sortBy;
      this.filter.sortOrder = 'desc';
    }
    this.applyFilters();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  onViewOrder(order: OrderSummary): void {
    this.loadOrderDetails(order.id);
  }

  private loadOrderDetails(orderId: number): void {
    const orderSub = this.orderService.getOrderById(orderId).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.selectedOrder = response.data;
          this.showOrderDetails = true;
        }
      },
      error: (error) => {
        console.error('Error loading order details:', error);
      }
    });

    this.subscriptions.push(orderSub);
  }

  onUpdateOrderStatus(order: OrderSummary): void {
    this.selectedOrder = order as any;
    this.newStatus = order.status;
    this.showStatusUpdateModal = true;
  }

  confirmStatusUpdate(): void {
    if (this.selectedOrder) {
      // Update order status logic here
      const orderIndex = this.orders.findIndex(o => o.id === this.selectedOrder!.id);
      if (orderIndex !== -1) {
        this.orders[orderIndex].status = this.newStatus;
        this.calculateStats();
        this.applyFilters();
      }
    }
    this.closeStatusUpdateModal();
  }

  closeOrderDetails(): void {
    this.showOrderDetails = false;
    this.selectedOrder = null;
  }

  closeStatusUpdateModal(): void {
    this.showStatusUpdateModal = false;
    this.selectedOrder = null;
  }

  getStatusColor(status: OrderStatus): string {
    const colors = {
      pending: '#ffc107',
      confirmed: '#28a745',
      processing: '#17a2b8',
      shipped: '#6f42c1',
      delivered: '#28a745',
      cancelled: '#dc3545',
      refunded: '#6c757d',
      returned: '#fd7e14'
    };
    return colors[status] || '#6c757d';
  }

  getStatusText(status: OrderStatus): string {
    const statusMap: Record<OrderStatus, string> = {
      'pending': 'En attente',
      'confirmed': 'Confirmée',
      'processing': 'En préparation',
      'shipped': 'Expédiée',
      'delivered': 'Livrée',
      'cancelled': 'Annulée',
      'refunded': 'Remboursée',
      'returned': 'Retournée'
    };
    return statusMap[status] || status;
  }

  trackByOrder(index: number, order: OrderSummary): number {
    return order.id;
  }

  formatCurrency(amount: number, currency: string = 'TND'): string {
    return `${amount.toFixed(2)} ${currency}`;
  }
}
