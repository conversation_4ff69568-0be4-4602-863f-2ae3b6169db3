import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { OrderService } from '../../../services/order';
import { ProductService } from '../../../services/product';
import { 
  LoadingState,
  OrderStatus 
} from '../../../interfaces';

interface AnalyticsData {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  conversionRate: number;
  topProducts: ProductAnalytics[];
  salesByMonth: MonthlyData[];
  ordersByStatus: StatusData[];
  revenueByCategory: CategoryData[];
}

interface ProductAnalytics {
  id: number;
  name: string;
  totalSold: number;
  revenue: number;
  category: string;
}

interface MonthlyData {
  month: string;
  revenue: number;
  orders: number;
}

interface StatusData {
  status: OrderStatus;
  count: number;
  percentage: number;
}

interface CategoryData {
  category: string;
  revenue: number;
  percentage: number;
}

interface DateRange {
  startDate: string;
  endDate: string;
}

@Component({
  selector: 'app-admin-analytics',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './analytics.html',
  styleUrls: ['./analytics.css']
})
export class AdminAnalyticsComponent implements OnInit, OnDestroy {
  analyticsData: AnalyticsData = {
    totalRevenue: 0,
    totalOrders: 0,
    averageOrderValue: 0,
    conversionRate: 0,
    topProducts: [],
    salesByMonth: [],
    ordersByStatus: [],
    revenueByCategory: []
  };

  loadingState: LoadingState = {
    isLoading: true,
    error: undefined
  };

  dateRange: DateRange = {
    startDate: '',
    endDate: ''
  };

  selectedPeriod: 'week' | 'month' | 'quarter' | 'year' | 'custom' = 'month';

  private subscriptions: Subscription[] = [];

  constructor(
    private orderService: OrderService,
    private productService: ProductService,
    public router: Router
  ) {}

  ngOnInit(): void {
    this.initializeDateRange();
    this.loadAnalytics();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private initializeDateRange(): void {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    
    this.dateRange = {
      startDate: startOfMonth.toISOString().split('T')[0],
      endDate: now.toISOString().split('T')[0]
    };
  }

  loadAnalytics(): void {
    this.loadingState.isLoading = true;
    
    // Mock analytics data - replace with actual service calls
    setTimeout(() => {
      this.analyticsData = this.generateMockAnalytics();
      this.loadingState.isLoading = false;
    }, 1500);
  }

  private generateMockAnalytics(): AnalyticsData {
    return {
      totalRevenue: 45750.50,
      totalOrders: 127,
      averageOrderValue: 360.24,
      conversionRate: 3.2,
      topProducts: [
        {
          id: 1,
          name: 'Casque Shark Speed-R',
          totalSold: 25,
          revenue: 10000,
          category: 'Casques'
        },
        {
          id: 2,
          name: 'Gants Alpinestars GP Pro',
          totalSold: 18,
          revenue: 2160,
          category: 'Gants'
        },
        {
          id: 3,
          name: 'Blouson Dainese Racing',
          totalSold: 12,
          revenue: 4200,
          category: 'Blousons'
        },
        {
          id: 4,
          name: 'Bottes TCX Street Ace',
          totalSold: 15,
          revenue: 3750,
          category: 'Chaussures'
        },
        {
          id: 5,
          name: 'Pantalon Kevlar Moto',
          totalSold: 20,
          revenue: 4000,
          category: 'Pantalons'
        }
      ],
      salesByMonth: [
        { month: 'Jan', revenue: 12500, orders: 35 },
        { month: 'Fév', revenue: 15200, orders: 42 },
        { month: 'Mar', revenue: 18750, orders: 52 },
        { month: 'Avr', revenue: 22100, orders: 61 },
        { month: 'Mai', revenue: 19800, orders: 55 },
        { month: 'Jun', revenue: 25300, orders: 70 },
        { month: 'Jul', revenue: 28900, orders: 80 },
        { month: 'Aoû', revenue: 31200, orders: 86 },
        { month: 'Sep', revenue: 35600, orders: 98 },
        { month: 'Oct', revenue: 45750, orders: 127 }
      ],
      ordersByStatus: [
        { status: 'delivered', count: 85, percentage: 66.9 },
        { status: 'shipped', count: 20, percentage: 15.7 },
        { status: 'confirmed', count: 12, percentage: 9.4 },
        { status: 'pending', count: 8, percentage: 6.3 },
        { status: 'cancelled', count: 2, percentage: 1.6 }
      ],
      revenueByCategory: [
        { category: 'Casques', revenue: 15200, percentage: 33.2 },
        { category: 'Blousons', revenue: 12800, percentage: 28.0 },
        { category: 'Gants', revenue: 8500, percentage: 18.6 },
        { category: 'Chaussures', revenue: 5750, percentage: 12.6 },
        { category: 'Pantalons', revenue: 3500, percentage: 7.6 }
      ]
    };
  }

  onPeriodChange(): void {
    if (this.selectedPeriod !== 'custom') {
      this.updateDateRangeForPeriod();
    }
    this.loadAnalytics();
  }

  private updateDateRangeForPeriod(): void {
    const now = new Date();
    let startDate: Date;

    switch (this.selectedPeriod) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        startDate = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        return;
    }

    this.dateRange = {
      startDate: startDate.toISOString().split('T')[0],
      endDate: now.toISOString().split('T')[0]
    };
  }

  onDateRangeChange(): void {
    this.selectedPeriod = 'custom';
    this.loadAnalytics();
  }

  getStatusColor(status: OrderStatus): string {
    const colors = {
      pending: '#ffc107',
      confirmed: '#28a745',
      processing: '#17a2b8',
      shipped: '#6f42c1',
      delivered: '#28a745',
      cancelled: '#dc3545',
      refunded: '#6c757d',
      returned: '#fd7e14'
    };
    return colors[status] || '#6c757d';
  }

  getStatusText(status: OrderStatus): string {
    const statusMap: Record<OrderStatus, string> = {
      'pending': 'En attente',
      'confirmed': 'Confirmées',
      'processing': 'En préparation',
      'shipped': 'Expédiées',
      'delivered': 'Livrées',
      'cancelled': 'Annulées',
      'refunded': 'Remboursées',
      'returned': 'Retournées'
    };
    return statusMap[status] || status;
  }

  formatCurrency(amount: number): string {
    return `${amount.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} TND`;
  }

  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  getMaxRevenueForChart(): number {
    return Math.max(...this.analyticsData.salesByMonth.map(item => item.revenue));
  }

  getMaxOrdersForChart(): number {
    return Math.max(...this.analyticsData.salesByMonth.map(item => item.orders));
  }

  onExportData(): void {
    // Mock export functionality
    const dataStr = JSON.stringify(this.analyticsData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `analytics-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }

  onViewProductDetails(product: ProductAnalytics): void {
    this.router.navigate(['/admin/products', product.id]);
  }

  trackByProduct(index: number, product: ProductAnalytics): number {
    return product.id;
  }

  trackByMonth(index: number, month: MonthlyData): string {
    return month.month;
  }

  trackByStatus(index: number, status: StatusData): string {
    return status.status;
  }

  trackByCategory(index: number, category: CategoryData): string {
    return category.category;
  }
}
