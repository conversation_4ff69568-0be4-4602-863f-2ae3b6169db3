<div class="admin-users">
  <!-- Header -->
  <div class="users-header">
    <div class="header-content">
      <h1>Gestion des Utilisateurs 👥</h1>
      <p class="header-subtitle"><PERSON><PERSON><PERSON> tous les utilisateurs de votre boutique</p>
    </div>
    <div class="header-actions">
      <button class="refresh-btn" (click)="loadUsers()" [disabled]="loadingState.isLoading">
        🔄 Actualiser
      </button>
    </div>
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement des utilisateurs...</p>
    </div>
  }

  @if (!loadingState.isLoading) {
    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card total">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <h3>{{ userStats.total }}</h3>
          <p>Total Utilisateurs</p>
        </div>
      </div>

      <div class="stat-card active">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <h3>{{ userStats.active }}</h3>
          <p>Actifs</p>
        </div>
      </div>

      <div class="stat-card inactive">
        <div class="stat-icon">⏸️</div>
        <div class="stat-content">
          <h3>{{ userStats.inactive }}</h3>
          <p>Inactifs</p>
        </div>
      </div>

      <div class="stat-card admins">
        <div class="stat-icon">👑</div>
        <div class="stat-content">
          <h3>{{ userStats.admins }}</h3>
          <p>Administrateurs</p>
        </div>
      </div>

      <div class="stat-card new">
        <div class="stat-icon">🆕</div>
        <div class="stat-content">
          <h3>{{ userStats.newThisMonth }}</h3>
          <p>Nouveaux ce mois</p>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section">
      <div class="search-container">
        <input 
          type="text" 
          placeholder="Rechercher par nom ou email..." 
          [(ngModel)]="filter.searchTerm"
          (input)="onSearchChange()"
          class="search-input">
        <span class="search-icon">🔍</span>
      </div>

      <div class="filter-controls">
        <select 
          [(ngModel)]="filter.role" 
          (change)="onFilterChange()"
          class="role-filter">
          <option value="all">Tous les rôles</option>
          <option value="admin">Administrateurs</option>
          <option value="user">Utilisateurs</option>
        </select>

        <select 
          [(ngModel)]="filter.status" 
          (change)="onFilterChange()"
          class="status-filter">
          <option value="all">Tous les statuts</option>
          <option value="active">Actifs</option>
          <option value="inactive">Inactifs</option>
        </select>

        <div class="sort-controls">
          <label>Trier par:</label>
          <button 
            class="sort-btn"
            [class.active]="filter.sortBy === 'name'"
            (click)="onSortChange('name')">
            Nom {{ filter.sortBy === 'name' ? (filter.sortOrder === 'asc' ? '↑' : '↓') : '' }}
          </button>
          <button 
            class="sort-btn"
            [class.active]="filter.sortBy === 'email'"
            (click)="onSortChange('email')">
            Email {{ filter.sortBy === 'email' ? (filter.sortOrder === 'asc' ? '↑' : '↓') : '' }}
          </button>
          <button 
            class="sort-btn"
            [class.active]="filter.sortBy === 'createdAt'"
            (click)="onSortChange('createdAt')">
            Inscription {{ filter.sortBy === 'createdAt' ? (filter.sortOrder === 'asc' ? '↑' : '↓') : '' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Users Table -->
    <div class="users-table-container">
      <table class="users-table">
        <thead>
          <tr>
            <th>Utilisateur</th>
            <th>Email</th>
            <th>Rôle</th>
            <th>Statut</th>
            <th>Inscription</th>
            <th>Dernière Connexion</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @if (paginatedUsers.length > 0) {
            @for (user of paginatedUsers; track trackByUser($index, user)) {
              <tr class="user-row">
                <td class="user-info-cell">
                  <div class="user-avatar">
                    {{ user.firstName.charAt(0) }}{{ user.lastName.charAt(0) }}
                  </div>
                  <div class="user-details">
                    <div class="user-name">{{ user.firstName }} {{ user.lastName }}</div>
                    <div class="user-id">ID: {{ user.id }}</div>
                  </div>
                </td>
                <td class="email-cell">{{ user.email }}</td>
                <td class="role-cell">
                  <span 
                    class="role-badge"
                    [class]="user.role || 'user'">
                    {{ getRoleText(user.role || 'user') }}
                  </span>
                </td>
                <td class="status-cell">
                  <span 
                    class="status-badge"
                    [class]="getUserStatusClass(user)">
                    {{ getUserStatusText(user) }}
                  </span>
                </td>
                <td class="date-cell">
                  {{ user.createdAt | date:'dd/MM/yyyy':'fr' }}
                </td>
                <td class="date-cell">
                  @if (user.lastLogin || user.lastLoginAt) {
                    {{ formatTimeAgo(user.lastLogin || user.lastLoginAt!) }}
                  } @else {
                    <span class="no-login">Jamais</span>
                  }
                </td>
                <td class="actions-cell">
                  <div class="action-buttons">
                    <button 
                      class="action-btn view"
                      (click)="onViewUser(user)"
                      title="Voir détails">
                      👁️
                    </button>
                    <button 
                      class="action-btn edit"
                      (click)="onEditUser(user)"
                      title="Modifier">
                      ✏️
                    </button>
                    <button 
                      class="action-btn toggle"
                      [class]="user.isActive ? 'deactivate' : 'activate'"
                      (click)="onToggleUserStatus(user)"
                      [title]="user.isActive ? 'Désactiver' : 'Activer'">
                      {{ user.isActive ? '⏸️' : '▶️' }}
                    </button>
                    <button 
                      class="action-btn delete"
                      (click)="onDeleteUser(user)"
                      title="Supprimer">
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            }
          } @else {
            <tr>
              <td colspan="7" class="empty-state">
                <div class="empty-content">
                  <div class="empty-icon">👥</div>
                  <p>Aucun utilisateur trouvé</p>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    @if (totalPages > 1) {
      <div class="pagination">
        <button 
          class="page-btn"
          [disabled]="currentPage === 1"
          (click)="onPageChange(currentPage - 1)">
          ← Précédent
        </button>
        
        @for (page of [].constructor(totalPages); track $index) {
          <button 
            class="page-btn"
            [class.active]="currentPage === $index + 1"
            (click)="onPageChange($index + 1)">
            {{ $index + 1 }}
          </button>
        }
        
        <button 
          class="page-btn"
          [disabled]="currentPage === totalPages"
          (click)="onPageChange(currentPage + 1)">
          Suivant →
        </button>
      </div>
    }
  }
</div>

<!-- User Details Modal -->
@if (showUserDetails && selectedUser) {
  <div class="modal-overlay" (click)="closeUserDetails()">
    <div class="modal-content user-details-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>Détails de l'Utilisateur</h2>
        <button class="close-btn" (click)="closeUserDetails()">✕</button>
      </div>
      
      <div class="modal-body">
        <div class="user-profile">
          <div class="profile-avatar">
            {{ selectedUser.firstName.charAt(0) }}{{ selectedUser.lastName.charAt(0) }}
          </div>
          <div class="profile-info">
            <h3>{{ selectedUser.firstName }} {{ selectedUser.lastName }}</h3>
            <p class="profile-email">{{ selectedUser.email }}</p>
            <div class="profile-badges">
              <span class="role-badge" [class]="selectedUser.role || 'user'">
                {{ getRoleText(selectedUser.role || 'user') }}
              </span>
              <span class="status-badge" [class]="getUserStatusClass(selectedUser)">
                {{ getUserStatusText(selectedUser) }}
              </span>
            </div>
          </div>
        </div>

        <div class="user-stats-grid">
          <div class="stat-item">
            <strong>ID Utilisateur</strong>
            <span>{{ selectedUser.id }}</span>
          </div>
          <div class="stat-item">
            <strong>Date d'inscription</strong>
            <span>{{ selectedUser.createdAt | date:'dd/MM/yyyy HH:mm':'fr' }}</span>
          </div>
          <div class="stat-item">
            <strong>Dernière connexion</strong>
            <span>
              @if (selectedUser.lastLogin || selectedUser.lastLoginAt) {
                {{ (selectedUser.lastLogin || selectedUser.lastLoginAt) | date:'dd/MM/yyyy HH:mm':'fr' }}
              } @else {
                Jamais connecté
              }
            </span>
          </div>
          <div class="stat-item">
            <strong>Statut du compte</strong>
            <span [class]="getUserStatusClass(selectedUser)">
              {{ getUserStatusText(selectedUser) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
}

<!-- Edit User Modal -->
@if (showEditUserModal && selectedUser) {
  <div class="modal-overlay" (click)="closeEditUserModal()">
    <div class="modal-content edit-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>Modifier l'Utilisateur</h2>
        <button class="close-btn" (click)="closeEditUserModal()">✕</button>
      </div>
      
      <div class="modal-body">
        <form class="edit-form">
          <div class="form-row">
            <div class="form-group">
              <label for="firstName">Prénom</label>
              <input 
                id="firstName" 
                type="text" 
                [(ngModel)]="editForm.firstName" 
                name="firstName"
                class="form-input">
            </div>
            <div class="form-group">
              <label for="lastName">Nom</label>
              <input 
                id="lastName" 
                type="text" 
                [(ngModel)]="editForm.lastName" 
                name="lastName"
                class="form-input">
            </div>
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input 
              id="email" 
              type="email" 
              [(ngModel)]="editForm.email" 
              name="email"
              class="form-input">
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="role">Rôle</label>
              <select 
                id="role" 
                [(ngModel)]="editForm.role" 
                name="role"
                class="form-select">
                <option value="user">Utilisateur</option>
                <option value="admin">Administrateur</option>
              </select>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="editForm.isActive" 
                  name="isActive"
                  class="form-checkbox">
                <span class="checkbox-text">Compte actif</span>
              </label>
            </div>
          </div>
        </form>
        
        <div class="modal-actions">
          <button class="cancel-btn" (click)="closeEditUserModal()">Annuler</button>
          <button class="confirm-btn" (click)="confirmEdit()">Sauvegarder</button>
        </div>
      </div>
    </div>
  </div>
}

<!-- Delete Confirmation Modal -->
@if (showDeleteConfirm && selectedUser) {
  <div class="modal-overlay" (click)="closeDeleteConfirm()">
    <div class="modal-content delete-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>Confirmer la Suppression</h2>
        <button class="close-btn" (click)="closeDeleteConfirm()">✕</button>
      </div>
      
      <div class="modal-body">
        <div class="delete-warning">
          <div class="warning-icon">⚠️</div>
          <p>Êtes-vous sûr de vouloir supprimer l'utilisateur <strong>{{ selectedUser.firstName }} {{ selectedUser.lastName }}</strong> ?</p>
          <p class="warning-text">Cette action est irréversible.</p>
        </div>
        
        <div class="modal-actions">
          <button class="cancel-btn" (click)="closeDeleteConfirm()">Annuler</button>
          <button class="delete-btn" (click)="confirmDelete()">Supprimer</button>
        </div>
      </div>
    </div>
  </div>
}
