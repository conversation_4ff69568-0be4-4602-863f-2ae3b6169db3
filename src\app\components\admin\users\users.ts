import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../services/auth';
import { 
  User, 
  LoadingState,
  ApiResponse 
} from '../../../interfaces';

interface UserStats {
  total: number;
  active: number;
  inactive: number;
  admins: number;
  newThisMonth: number;
}

interface UserFilter {
  searchTerm: string;
  role: 'all' | 'admin' | 'user';
  status: 'all' | 'active' | 'inactive';
  sortBy: 'name' | 'email' | 'createdAt' | 'lastLogin';
  sortOrder: 'asc' | 'desc';
}

@Component({
  selector: 'app-admin-users',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './users.html',
  styleUrls: ['./users.css']
})
export class AdminUsersComponent implements OnInit, OnDestroy {
  users: User[] = [];
  filteredUsers: User[] = [];
  selectedUser: User | null = null;
  
  loadingState: LoadingState = {
    isLoading: true,
    error: undefined
  };

  userStats: UserStats = {
    total: 0,
    active: 0,
    inactive: 0,
    admins: 0,
    newThisMonth: 0
  };

  filter: UserFilter = {
    searchTerm: '',
    role: 'all',
    status: 'all',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  };

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  // UI states
  showUserDetails = false;
  showEditUserModal = false;
  showDeleteConfirm = false;

  // Edit form
  editForm = {
    firstName: '',
    lastName: '',
    email: '',
    role: 'user' as 'admin' | 'user',
    isActive: true
  };

  private subscriptions: Subscription[] = [];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUsers();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadUsers(): void {
    this.loadingState.isLoading = true;
    
    // Mock users data - replace with actual service call
    setTimeout(() => {
      this.users = this.generateMockUsers();
      this.calculateStats();
      this.applyFilters();
      this.loadingState.isLoading = false;
    }, 1000);
  }

  private generateMockUsers(): User[] {
    return [
      {
        id: 1,
        firstName: 'Amine',
        lastName: 'Hbili',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
        createdAt: new Date('2024-01-15'),
        lastLogin: new Date('2025-10-06T10:30:00'),
        addresses: []
      },
      {
        id: 2,
        firstName: 'Sarah',
        lastName: 'Ben Ali',
        email: '<EMAIL>',
        role: 'user',
        isActive: true,
        createdAt: new Date('2024-03-20'),
        lastLogin: new Date('2025-10-05T14:20:00'),
        addresses: []
      },
      {
        id: 3,
        firstName: 'Mohamed',
        lastName: 'Trabelsi',
        email: '<EMAIL>',
        role: 'user',
        isActive: true,
        createdAt: new Date('2024-05-10'),
        lastLogin: new Date('2025-10-04T09:15:00'),
        addresses: []
      },
      {
        id: 4,
        firstName: 'Fatma',
        lastName: 'Khelifi',
        email: '<EMAIL>',
        role: 'user',
        isActive: false,
        createdAt: new Date('2024-07-25'),
        lastLogin: new Date('2025-09-20T16:45:00'),
        addresses: []
      },
      {
        id: 5,
        firstName: 'Ahmed',
        lastName: 'Mansouri',
        email: '<EMAIL>',
        role: 'user',
        isActive: true,
        createdAt: new Date('2024-09-12'),
        lastLogin: new Date('2025-10-03T11:30:00'),
        addresses: []
      }
    ];
  }

  private calculateStats(): void {
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    this.userStats = {
      total: this.users.length,
      active: this.users.filter(u => u.isActive).length,
      inactive: this.users.filter(u => !u.isActive).length,
      admins: this.users.filter(u => u.role === 'admin').length,
      newThisMonth: this.users.filter(u => u.createdAt >= thisMonth).length
    };
  }

  applyFilters(): void {
    let filtered = [...this.users];

    // Search filter
    if (this.filter.searchTerm.trim()) {
      const term = this.filter.searchTerm.toLowerCase();
      filtered = filtered.filter(user => 
        user.firstName.toLowerCase().includes(term) ||
        user.lastName.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term)
      );
    }

    // Role filter
    if (this.filter.role !== 'all') {
      filtered = filtered.filter(user => user.role === this.filter.role);
    }

    // Status filter
    if (this.filter.status !== 'all') {
      const isActive = this.filter.status === 'active';
      filtered = filtered.filter(user => user.isActive === isActive);
    }

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (this.filter.sortBy) {
        case 'name':
          comparison = `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
          break;
        case 'email':
          comparison = a.email.localeCompare(b.email);
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'lastLogin':
          const aLogin = a.lastLogin ? new Date(a.lastLogin).getTime() : 0;
          const bLogin = b.lastLogin ? new Date(b.lastLogin).getTime() : 0;
          comparison = aLogin - bLogin;
          break;
        default:
          comparison = a.id - b.id;
      }

      return this.filter.sortOrder === 'desc' ? -comparison : comparison;
    });

    this.filteredUsers = filtered;
    this.totalPages = Math.ceil(filtered.length / this.itemsPerPage);
    this.currentPage = Math.min(this.currentPage, this.totalPages || 1);
  }

  get paginatedUsers(): User[] {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return this.filteredUsers.slice(start, end);
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onFilterChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onSortChange(sortBy: 'name' | 'email' | 'createdAt' | 'lastLogin'): void {
    if (this.filter.sortBy === sortBy) {
      this.filter.sortOrder = this.filter.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.filter.sortBy = sortBy;
      this.filter.sortOrder = 'desc';
    }
    this.applyFilters();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  onViewUser(user: User): void {
    this.selectedUser = user;
    this.showUserDetails = true;
  }

  onEditUser(user: User): void {
    this.selectedUser = user;
    this.editForm = {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      isActive: user.isActive
    };
    this.showEditUserModal = true;
  }

  onDeleteUser(user: User): void {
    this.selectedUser = user;
    this.showDeleteConfirm = true;
  }

  onToggleUserStatus(user: User): void {
    const userIndex = this.users.findIndex(u => u.id === user.id);
    if (userIndex !== -1) {
      this.users[userIndex].isActive = !this.users[userIndex].isActive;
      this.calculateStats();
      this.applyFilters();
    }
  }

  confirmEdit(): void {
    if (this.selectedUser) {
      const userIndex = this.users.findIndex(u => u.id === this.selectedUser!.id);
      if (userIndex !== -1) {
        this.users[userIndex] = {
          ...this.users[userIndex],
          firstName: this.editForm.firstName,
          lastName: this.editForm.lastName,
          email: this.editForm.email,
          role: this.editForm.role,
          isActive: this.editForm.isActive
        };
        this.calculateStats();
        this.applyFilters();
      }
    }
    this.closeEditUserModal();
  }

  confirmDelete(): void {
    if (this.selectedUser) {
      const index = this.users.findIndex(u => u.id === this.selectedUser!.id);
      if (index !== -1) {
        this.users.splice(index, 1);
        this.calculateStats();
        this.applyFilters();
      }
    }
    this.closeDeleteConfirm();
  }

  closeUserDetails(): void {
    this.showUserDetails = false;
    this.selectedUser = null;
  }

  closeEditUserModal(): void {
    this.showEditUserModal = false;
    this.selectedUser = null;
  }

  closeDeleteConfirm(): void {
    this.showDeleteConfirm = false;
    this.selectedUser = null;
  }

  getUserStatusText(user: User): string {
    return user.isActive ? 'Actif' : 'Inactif';
  }

  getUserStatusClass(user: User): string {
    return user.isActive ? 'active' : 'inactive';
  }

  getRoleText(role: string): string {
    return role === 'admin' ? 'Administrateur' : 'Utilisateur';
  }

  formatTimeAgo(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) return 'Aujourd\'hui';
    if (days === 1) return 'Hier';
    if (days < 7) return `Il y a ${days} jours`;
    if (days < 30) return `Il y a ${Math.floor(days / 7)} semaines`;
    if (days < 365) return `Il y a ${Math.floor(days / 30)} mois`;
    return `Il y a ${Math.floor(days / 365)} ans`;
  }

  trackByUser(index: number, user: User): number {
    return user.id;
  }
}
