<div class="admin-layout" [class.sidebar-collapsed]="isSidebarCollapsed">
  <!-- Sidebar -->
  <aside class="sidebar" [class.mobile-open]="isMobileMenuOpen">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
      <div class="logo">
        <span class="logo-icon">🏍️</span>
        @if (!isSidebarCollapsed) {
          <span class="logo-text">MotoShop Admin</span>
        }
      </div>
      
      @if (!isSidebarCollapsed) {
        <button class="sidebar-toggle" (click)="onToggleSidebar()">
          ←
        </button>
      }
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
      <ul class="nav-list">
        @for (item of menuItems; track trackByMenuItem($index, item)) {
          <li class="nav-item">
            <a 
              class="nav-link"
              [class.active]="isRouteActive(item.route)"
              (click)="onNavigate(item.route)">
              <span class="nav-icon">{{ item.icon }}</span>
              @if (!isSidebarCollapsed) {
                <span class="nav-label">{{ item.label }}</span>
                @if (item.badge && item.badge > 0) {
                  <span class="nav-badge">{{ item.badge }}</span>
                }
              }
            </a>
          </li>
        }
      </ul>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
      @if (!isSidebarCollapsed) {
        <div class="user-info">
          <div class="user-avatar">
            @if (userInfo.avatar) {
              <img [src]="userInfo.avatar" [alt]="userInfo.name" />
            } @else {
              <span class="avatar-initials">{{ getUserInitials() }}</span>
            }
          </div>
          <div class="user-details">
            <div class="user-name">{{ userInfo.name }}</div>
            <div class="user-role">{{ userInfo.role }}</div>
          </div>
        </div>
      }
    </div>
  </aside>

  <!-- Mobile Overlay -->
  @if (isMobileMenuOpen) {
    <div class="mobile-overlay" (click)="onCloseMobileMenu()"></div>
  }

  <!-- Main Content -->
  <div class="main-content">
    <!-- Top Header -->
    <header class="top-header">
      <div class="header-left">
        <!-- Mobile Menu Toggle -->
        <button class="mobile-menu-toggle" (click)="onToggleMobileMenu()">
          ☰
        </button>
        
        <!-- Sidebar Toggle (Desktop) -->
        @if (isSidebarCollapsed) {
          <button class="sidebar-expand" (click)="onToggleSidebar()">
            →
          </button>
        }

        <!-- Breadcrumbs -->
        <div class="breadcrumbs">
          <span class="breadcrumb-item">Admin</span>
          @for (crumb of getBreadcrumbs(); track $index) {
            <span class="breadcrumb-separator">›</span>
            <span class="breadcrumb-item">{{ crumb }}</span>
          }
        </div>
      </div>

      <div class="header-right">
        <!-- Notifications -->
        <button class="header-btn notifications" (click)="onViewNotifications()">
          🔔
          @if (getNotificationCount() > 0) {
            <span class="notification-badge">{{ getNotificationCount() }}</span>
          }
        </button>

        <!-- Go to Site -->
        <button class="header-btn site-link" (click)="onGoToSite()" title="Voir le site">
          🌐
        </button>

        <!-- User Menu -->
        <div class="user-menu">
          <button class="user-menu-toggle" (click)="onViewProfile()">
            @if (userInfo.avatar) {
              <img [src]="userInfo.avatar" [alt]="userInfo.name" class="user-avatar-small" />
            } @else {
              <span class="user-avatar-small">{{ getUserInitials() }}</span>
            }
            <span class="user-name-header">{{ userInfo.name }}</span>
          </button>
        </div>

        <!-- Logout -->
        <button class="header-btn logout" (click)="onLogout()" title="Se déconnecter">
          🚪
        </button>
      </div>
    </header>

    <!-- Page Content -->
    <main class="page-content">
      <router-outlet></router-outlet>
    </main>

    <!-- Footer -->
    <footer class="admin-footer">
      <div class="footer-content">
        <div class="footer-left">
          <span>© 2025 MotoShop Tunisia. Tous droits réservés.</span>
        </div>
        <div class="footer-right">
          <span>Version 1.0.0</span>
        </div>
      </div>
    </footer>
  </div>
</div>
