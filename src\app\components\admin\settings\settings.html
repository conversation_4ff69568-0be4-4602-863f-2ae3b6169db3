<div class="admin-settings">
  <!-- Header -->
  <div class="settings-header">
    <div class="header-content">
      <h1>Paramètres ⚙️</h1>
      <p class="header-subtitle">Configurez votre boutique selon vos besoins</p>
    </div>
    <div class="header-actions">
      <button class="reset-btn" (click)="onResetToDefaults()" [disabled]="loadingState.isLoading">
        🔄 Restaurer
      </button>
    </div>
  </div>

  <!-- Tabs Navigation -->
  <div class="tabs-navigation">
    <button 
      class="tab-btn"
      [class.active]="activeTab === 'site'"
      (click)="onTabChange('site')">
      {{ getTabIcon('site') }} {{ getTabTitle('site') }}
    </button>
    <button 
      class="tab-btn"
      [class.active]="activeTab === 'payment'"
      (click)="onTabChange('payment')">
      {{ getTabIcon('payment') }} {{ getTabTitle('payment') }}
    </button>
    <button 
      class="tab-btn"
      [class.active]="activeTab === 'shipping'"
      (click)="onTabChange('shipping')">
      {{ getTabIcon('shipping') }} {{ getTabTitle('shipping') }}
    </button>
    <button 
      class="tab-btn"
      [class.active]="activeTab === 'email'"
      (click)="onTabChange('email')">
      {{ getTabIcon('email') }} {{ getTabTitle('email') }}
    </button>
    <button 
      class="tab-btn"
      [class.active]="activeTab === 'security'"
      (click)="onTabChange('security')">
      {{ getTabIcon('security') }} {{ getTabTitle('security') }}
    </button>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    <!-- Site Settings -->
    @if (activeTab === 'site') {
      <div class="settings-section">
        <div class="section-header">
          <h2>Paramètres du Site</h2>
          <p>Configurez les informations générales de votre boutique</p>
        </div>
        
        <form class="settings-form" (ngSubmit)="onSaveSiteSettings()">
          <div class="form-grid">
            <div class="form-group">
              <label for="siteName">Nom du Site</label>
              <input 
                id="siteName" 
                type="text" 
                [(ngModel)]="siteSettings.siteName" 
                name="siteName"
                class="form-input"
                required>
            </div>

            <div class="form-group">
              <label for="siteUrl">URL du Site</label>
              <input 
                id="siteUrl" 
                type="url" 
                [(ngModel)]="siteSettings.siteUrl" 
                name="siteUrl"
                class="form-input"
                required>
            </div>

            <div class="form-group full-width">
              <label for="siteDescription">Description</label>
              <textarea 
                id="siteDescription" 
                [(ngModel)]="siteSettings.siteDescription" 
                name="siteDescription"
                class="form-textarea"
                rows="3"></textarea>
            </div>

            <div class="form-group">
              <label for="contactEmail">Email de Contact</label>
              <input 
                id="contactEmail" 
                type="email" 
                [(ngModel)]="siteSettings.contactEmail" 
                name="contactEmail"
                class="form-input"
                required>
            </div>

            <div class="form-group">
              <label for="contactPhone">Téléphone</label>
              <input 
                id="contactPhone" 
                type="tel" 
                [(ngModel)]="siteSettings.contactPhone" 
                name="contactPhone"
                class="form-input">
            </div>

            <div class="form-group full-width">
              <label for="address">Adresse</label>
              <textarea 
                id="address" 
                [(ngModel)]="siteSettings.address" 
                name="address"
                class="form-textarea"
                rows="2"></textarea>
            </div>

            <div class="form-group">
              <label for="currency">Devise</label>
              <select 
                id="currency" 
                [(ngModel)]="siteSettings.currency" 
                name="currency"
                class="form-select">
                <option value="TND">Dinar Tunisien (TND)</option>
                <option value="EUR">Euro (EUR)</option>
                <option value="USD">Dollar US (USD)</option>
              </select>
            </div>

            <div class="form-group">
              <label for="language">Langue</label>
              <select 
                id="language" 
                [(ngModel)]="siteSettings.language" 
                name="language"
                class="form-select">
                <option value="fr">Français</option>
                <option value="ar">العربية</option>
                <option value="en">English</option>
              </select>
            </div>

            <div class="form-group">
              <label for="timezone">Fuseau Horaire</label>
              <select 
                id="timezone" 
                [(ngModel)]="siteSettings.timezone" 
                name="timezone"
                class="form-select">
                <option value="Africa/Tunis">Africa/Tunis</option>
                <option value="Europe/Paris">Europe/Paris</option>
                <option value="UTC">UTC</option>
              </select>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="siteSettings.maintenanceMode" 
                  name="maintenanceMode"
                  class="form-checkbox">
                <span class="checkbox-text">Mode Maintenance</span>
              </label>
              <small class="form-help">Active le mode maintenance pour les visiteurs</small>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="save-btn" [disabled]="loadingState.isLoading">
              @if (loadingState.isLoading) {
                <span class="loading-spinner-small"></span>
              }
              Sauvegarder
            </button>
          </div>
        </form>
      </div>
    }

    <!-- Payment Settings -->
    @if (activeTab === 'payment') {
      <div class="settings-section">
        <div class="section-header">
          <h2>Paramètres de Paiement</h2>
          <p>Configurez les méthodes de paiement acceptées</p>
        </div>
        
        <form class="settings-form" (ngSubmit)="onSavePaymentSettings()">
          <div class="form-section">
            <h3>Méthodes de Paiement</h3>
            <div class="checkbox-grid">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="paymentSettings.enableCreditCard" 
                  name="enableCreditCard"
                  class="form-checkbox">
                <span class="checkbox-text">💳 Carte de Crédit</span>
              </label>

              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="paymentSettings.enablePaypal" 
                  name="enablePaypal"
                  class="form-checkbox">
                <span class="checkbox-text">🅿️ PayPal</span>
              </label>

              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="paymentSettings.enableBankTransfer" 
                  name="enableBankTransfer"
                  class="form-checkbox">
                <span class="checkbox-text">🏦 Virement Bancaire</span>
              </label>

              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="paymentSettings.enableCashOnDelivery" 
                  name="enableCashOnDelivery"
                  class="form-checkbox">
                <span class="checkbox-text">💵 Paiement à la Livraison</span>
              </label>
            </div>
          </div>

          <div class="form-section">
            <h3>Configuration des Passerelles</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="paypalClientId">PayPal Client ID</label>
                <input 
                  id="paypalClientId" 
                  type="text" 
                  [(ngModel)]="paymentSettings.paypalClientId" 
                  name="paypalClientId"
                  class="form-input"
                  placeholder="Votre PayPal Client ID">
              </div>

              <div class="form-group">
                <label for="stripePublicKey">Stripe Public Key</label>
                <input 
                  id="stripePublicKey" 
                  type="text" 
                  [(ngModel)]="paymentSettings.stripePublicKey" 
                  name="stripePublicKey"
                  class="form-input"
                  placeholder="pk_test_...">
              </div>

              <div class="form-group full-width">
                <label for="bankAccountDetails">Détails du Compte Bancaire</label>
                <textarea 
                  id="bankAccountDetails" 
                  [(ngModel)]="paymentSettings.bankAccountDetails" 
                  name="bankAccountDetails"
                  class="form-textarea"
                  rows="3"
                  placeholder="IBAN, RIB, etc."></textarea>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="save-btn" [disabled]="loadingState.isLoading">
              @if (loadingState.isLoading) {
                <span class="loading-spinner-small"></span>
              }
              Sauvegarder
            </button>
          </div>
        </form>
      </div>
    }

    <!-- Shipping Settings -->
    @if (activeTab === 'shipping') {
      <div class="settings-section">
        <div class="section-header">
          <h2>Paramètres de Livraison</h2>
          <p>Configurez les options et coûts de livraison</p>
        </div>
        
        <form class="settings-form" (ngSubmit)="onSaveShippingSettings()">
          <div class="form-section">
            <h3>Coûts de Livraison</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="freeShippingThreshold">Seuil Livraison Gratuite (TND)</label>
                <input 
                  id="freeShippingThreshold" 
                  type="number" 
                  [(ngModel)]="shippingSettings.freeShippingThreshold" 
                  name="freeShippingThreshold"
                  class="form-input"
                  min="0"
                  step="0.01">
              </div>

              <div class="form-group">
                <label for="standardShippingCost">Livraison Standard (TND)</label>
                <input 
                  id="standardShippingCost" 
                  type="number" 
                  [(ngModel)]="shippingSettings.standardShippingCost" 
                  name="standardShippingCost"
                  class="form-input"
                  min="0"
                  step="0.01">
              </div>

              <div class="form-group">
                <label for="expressShippingCost">Livraison Express (TND)</label>
                <input 
                  id="expressShippingCost" 
                  type="number" 
                  [(ngModel)]="shippingSettings.expressShippingCost" 
                  name="expressShippingCost"
                  class="form-input"
                  min="0"
                  step="0.01">
              </div>

              <div class="form-group">
                <label for="internationalShippingCost">Livraison Internationale (TND)</label>
                <input 
                  id="internationalShippingCost" 
                  type="number" 
                  [(ngModel)]="shippingSettings.internationalShippingCost" 
                  name="internationalShippingCost"
                  class="form-input"
                  min="0"
                  step="0.01">
              </div>
            </div>
          </div>

          <div class="form-section">
            <h3>Délais de Livraison</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="processingDays">Jours de Traitement</label>
                <input 
                  id="processingDays" 
                  type="number" 
                  [(ngModel)]="shippingSettings.processingDays" 
                  name="processingDays"
                  class="form-input"
                  min="0">
              </div>

              <div class="form-group">
                <label for="standardDeliveryDays">Livraison Standard (jours)</label>
                <input 
                  id="standardDeliveryDays" 
                  type="number" 
                  [(ngModel)]="shippingSettings.standardDeliveryDays" 
                  name="standardDeliveryDays"
                  class="form-input"
                  min="1">
              </div>

              <div class="form-group">
                <label for="expressDeliveryDays">Livraison Express (jours)</label>
                <input 
                  id="expressDeliveryDays" 
                  type="number" 
                  [(ngModel)]="shippingSettings.expressDeliveryDays" 
                  name="expressDeliveryDays"
                  class="form-input"
                  min="1">
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    [(ngModel)]="shippingSettings.enableInternationalShipping" 
                    name="enableInternationalShipping"
                    class="form-checkbox">
                  <span class="checkbox-text">Activer la Livraison Internationale</span>
                </label>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="save-btn" [disabled]="loadingState.isLoading">
              @if (loadingState.isLoading) {
                <span class="loading-spinner-small"></span>
              }
              Sauvegarder
            </button>
          </div>
        </form>
      </div>
    }

    <!-- Email Settings -->
    @if (activeTab === 'email') {
      <div class="settings-section">
        <div class="section-header">
          <h2>Paramètres Email</h2>
          <p>Configurez les paramètres SMTP et notifications</p>
        </div>
        
        <form class="settings-form" (ngSubmit)="onSaveEmailSettings()">
          <div class="form-section">
            <h3>Configuration SMTP</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="smtpHost">Serveur SMTP</label>
                <input 
                  id="smtpHost" 
                  type="text" 
                  [(ngModel)]="emailSettings.smtpHost" 
                  name="smtpHost"
                  class="form-input"
                  placeholder="smtp.gmail.com">
              </div>

              <div class="form-group">
                <label for="smtpPort">Port SMTP</label>
                <input 
                  id="smtpPort" 
                  type="number" 
                  [(ngModel)]="emailSettings.smtpPort" 
                  name="smtpPort"
                  class="form-input"
                  placeholder="587">
              </div>

              <div class="form-group">
                <label for="smtpUsername">Nom d'utilisateur</label>
                <input 
                  id="smtpUsername" 
                  type="text" 
                  [(ngModel)]="emailSettings.smtpUsername" 
                  name="smtpUsername"
                  class="form-input">
              </div>

              <div class="form-group">
                <label for="smtpPassword">Mot de passe</label>
                <input 
                  id="smtpPassword" 
                  type="password" 
                  [(ngModel)]="emailSettings.smtpPassword" 
                  name="smtpPassword"
                  class="form-input">
              </div>

              <div class="form-group">
                <label for="fromEmail">Email Expéditeur</label>
                <input 
                  id="fromEmail" 
                  type="email" 
                  [(ngModel)]="emailSettings.fromEmail" 
                  name="fromEmail"
                  class="form-input"
                  placeholder="<EMAIL>">
              </div>

              <div class="form-group">
                <label for="fromName">Nom Expéditeur</label>
                <input 
                  id="fromName" 
                  type="text" 
                  [(ngModel)]="emailSettings.fromName" 
                  name="fromName"
                  class="form-input"
                  placeholder="MotoShop Tunisia">
              </div>
            </div>
          </div>

          <div class="form-section">
            <h3>Notifications Email</h3>
            <div class="checkbox-grid">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="emailSettings.enableOrderConfirmation" 
                  name="enableOrderConfirmation"
                  class="form-checkbox">
                <span class="checkbox-text">📧 Confirmation de Commande</span>
              </label>

              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="emailSettings.enableShippingNotification" 
                  name="enableShippingNotification"
                  class="form-checkbox">
                <span class="checkbox-text">🚚 Notification d'Expédition</span>
              </label>

              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="emailSettings.enableNewsletters" 
                  name="enableNewsletters"
                  class="form-checkbox">
                <span class="checkbox-text">📰 Newsletters</span>
              </label>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="test-btn" (click)="onTestEmailSettings()" [disabled]="loadingState.isLoading">
              🧪 Tester
            </button>
            <button type="submit" class="save-btn" [disabled]="loadingState.isLoading">
              @if (loadingState.isLoading) {
                <span class="loading-spinner-small"></span>
              }
              Sauvegarder
            </button>
          </div>
        </form>
      </div>
    }

    <!-- Security Settings -->
    @if (activeTab === 'security') {
      <div class="settings-section">
        <div class="section-header">
          <h2>Paramètres de Sécurité</h2>
          <p>Configurez la sécurité et l'authentification</p>
        </div>
        
        <form class="settings-form" (ngSubmit)="onSaveSecuritySettings()">
          <div class="form-section">
            <h3>Authentification</h3>
            <div class="form-grid">
              <div class="form-group">
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    [(ngModel)]="securitySettings.enableTwoFactor" 
                    name="enableTwoFactor"
                    class="form-checkbox">
                  <span class="checkbox-text">🔐 Authentification à Deux Facteurs</span>
                </label>
              </div>

              <div class="form-group">
                <label for="sessionTimeout">Timeout de Session (minutes)</label>
                <input 
                  id="sessionTimeout" 
                  type="number" 
                  [(ngModel)]="securitySettings.sessionTimeout" 
                  name="sessionTimeout"
                  class="form-input"
                  min="5"
                  max="480">
              </div>

              <div class="form-group">
                <label for="maxLoginAttempts">Tentatives de Connexion Max</label>
                <input 
                  id="maxLoginAttempts" 
                  type="number" 
                  [(ngModel)]="securitySettings.maxLoginAttempts" 
                  name="maxLoginAttempts"
                  class="form-input"
                  min="3"
                  max="10">
              </div>
            </div>
          </div>

          <div class="form-section">
            <h3>Politique des Mots de Passe</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="passwordMinLength">Longueur Minimale</label>
                <input 
                  id="passwordMinLength" 
                  type="number" 
                  [(ngModel)]="securitySettings.passwordMinLength" 
                  name="passwordMinLength"
                  class="form-input"
                  min="6"
                  max="20">
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    [(ngModel)]="securitySettings.requirePasswordComplexity" 
                    name="requirePasswordComplexity"
                    class="form-checkbox">
                  <span class="checkbox-text">🔤 Exiger la Complexité</span>
                </label>
                <small class="form-help">Majuscules, minuscules, chiffres et symboles</small>
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    [(ngModel)]="securitySettings.enableCaptcha" 
                    name="enableCaptcha"
                    class="form-checkbox">
                  <span class="checkbox-text">🤖 Activer CAPTCHA</span>
                </label>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="save-btn" [disabled]="loadingState.isLoading">
              @if (loadingState.isLoading) {
                <span class="loading-spinner-small"></span>
              }
              Sauvegarder
            </button>
          </div>
        </form>
      </div>
    }
  </div>
</div>
