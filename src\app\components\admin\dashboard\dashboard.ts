import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ProductService } from '../../../services/product';
import { OrderService } from '../../../services/order';
import { AuthService } from '../../../services/auth';
import { 
  Product, 
  Order, 
  User,
  LoadingState,
  OrderStatus 
} from '../../../interfaces';

interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalUsers: number;
  totalRevenue: number;
  pendingOrders: number;
  lowStockProducts: number;
  newUsersThisMonth: number;
  conversionRate: number;
}

interface RecentActivity {
  id: string;
  type: 'order' | 'product' | 'user';
  message: string;
  timestamp: Date;
  status?: 'success' | 'warning' | 'error';
}

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
  }[];
}

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dashboard.html',
  styleUrls: ['./dashboard.css']
})
export class AdminDashboardComponent implements OnInit, OnDestroy {
  stats: DashboardStats = {
    totalProducts: 0,
    totalOrders: 0,
    totalUsers: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    lowStockProducts: 0,
    newUsersThisMonth: 0,
    conversionRate: 0
  };

  recentOrders: Order[] = [];
  recentActivities: RecentActivity[] = [];
  topProducts: Product[] = [];
  
  loadingState: LoadingState = {
    isLoading: true,
    error: undefined
  };

  salesChartData: ChartData = {
    labels: [],
    datasets: []
  };

  ordersChartData: ChartData = {
    labels: [],
    datasets: []
  };

  private subscriptions: Subscription[] = [];

  constructor(
    private productService: ProductService,
    private orderService: OrderService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadDashboardData(): void {
    this.loadingState.isLoading = true;

    // Load products
    const productsSub = this.productService.getAllProducts().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.stats.totalProducts = response.data.length;
          this.stats.lowStockProducts = response.data.filter(p => (p.quantity || 0) < 10).length;
          this.topProducts = response.data.slice(0, 5);
        }
      },
      error: (error) => {
        console.error('Error loading products:', error);
      }
    });

    // Load orders
    const ordersSub = this.orderService.getUserOrders(1).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const orders = response.data.orders || [];
          this.stats.totalOrders = orders.length;
          this.stats.pendingOrders = orders.filter((o: any) => o.status === 'pending').length;
          this.stats.totalRevenue = orders.reduce((sum: number, order: any) => sum + order.pricing.total, 0);
          this.recentOrders = orders.slice(0, 5);

          this.generateChartData(orders);
        }
        this.loadingState.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading orders:', error);
        this.loadingState.isLoading = false;
        this.loadingState.error = 'Erreur lors du chargement des données';
      }
    });

    this.subscriptions.push(productsSub, ordersSub);

    // Generate mock data for other stats
    this.generateMockStats();
    this.generateRecentActivities();
  }

  private generateMockStats(): void {
    this.stats.totalUsers = 1250;
    this.stats.newUsersThisMonth = 85;
    this.stats.conversionRate = 3.2;
  }

  private generateRecentActivities(): void {
    this.recentActivities = [
      {
        id: '1',
        type: 'order',
        message: 'Nouvelle commande #ORD-2024-001',
        timestamp: new Date(Date.now() - 5 * 60 * 1000),
        status: 'success'
      },
      {
        id: '2',
        type: 'product',
        message: 'Stock faible: Casque Shark',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
        status: 'warning'
      },
      {
        id: '3',
        type: 'user',
        message: 'Nouvel utilisateur inscrit',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        status: 'success'
      },
      {
        id: '4',
        type: 'order',
        message: 'Commande #ORD-2024-002 expédiée',
        timestamp: new Date(Date.now() - 45 * 60 * 1000),
        status: 'success'
      },
      {
        id: '5',
        type: 'product',
        message: 'Nouveau produit ajouté: Gants Racing',
        timestamp: new Date(Date.now() - 60 * 60 * 1000),
        status: 'success'
      }
    ];
  }

  private generateChartData(orders: Order[]): void {
    // Sales chart data (last 7 days)
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date.toLocaleDateString('fr-FR', { weekday: 'short' });
    });

    const salesData = last7Days.map(() => Math.floor(Math.random() * 5000) + 1000);

    this.salesChartData = {
      labels: last7Days,
      datasets: [{
        label: 'Ventes (TND)',
        data: salesData,
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)'
      }]
    };

    // Orders chart data (by status)
    const statusCounts = {
      pending: orders.filter(o => o.status === 'pending').length,
      confirmed: orders.filter(o => o.status === 'confirmed').length,
      shipped: orders.filter(o => o.status === 'shipped').length,
      delivered: orders.filter(o => o.status === 'delivered').length,
      cancelled: orders.filter(o => o.status === 'cancelled').length
    };

    this.ordersChartData = {
      labels: ['En attente', 'Confirmées', 'Expédiées', 'Livrées', 'Annulées'],
      datasets: [{
        label: 'Commandes',
        data: Object.values(statusCounts),
        backgroundColor: [
          '#ffc107',
          '#28a745',
          '#17a2b8',
          '#6f42c1',
          '#dc3545'
        ]
      }]
    };
  }

  getStatusColor(status: OrderStatus): string {
    const colors = {
      pending: '#ffc107',
      confirmed: '#28a745',
      processing: '#17a2b8',
      shipped: '#6f42c1',
      delivered: '#28a745',
      cancelled: '#dc3545',
      refunded: '#6c757d',
      returned: '#fd7e14'
    };
    return colors[status] || '#6c757d';
  }

  getActivityIcon(type: string): string {
    const icons: { [key: string]: string } = {
      order: '📦',
      product: '🛍️',
      user: '👤'
    };
    return icons[type] || '📋';
  }

  formatTimeAgo(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) return 'À l\'instant';
    if (minutes < 60) return `Il y a ${minutes} min`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `Il y a ${hours}h`;
    
    const days = Math.floor(hours / 24);
    return `Il y a ${days}j`;
  }

  onNavigateToProducts(): void {
    this.router.navigate(['/admin/products']);
  }

  onNavigateToOrders(): void {
    this.router.navigate(['/admin/orders']);
  }

  onNavigateToUsers(): void {
    this.router.navigate(['/admin/users']);
  }

  onNavigateToAnalytics(): void {
    this.router.navigate(['/admin/analytics']);
  }

  onViewOrder(orderId: number): void {
    this.router.navigate(['/admin/orders', orderId]);
  }

  onViewProduct(productId: number): void {
    this.router.navigate(['/admin/products', productId]);
  }

  trackByActivity(index: number, activity: RecentActivity): string {
    return activity.id;
  }

  trackByOrder(index: number, order: Order): number {
    return order.id;
  }

  trackByProduct(index: number, product: Product): number {
    return product.id;
  }
}
