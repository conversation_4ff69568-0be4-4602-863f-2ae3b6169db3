import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ProductService } from '../../../services/product';
import { 
  Product, 
  ProductCategory, 
  ProductFilter,
  LoadingState,
  ApiResponse 
} from '../../../interfaces';

interface ProductFormData {
  id?: number;
  name: string;
  price: number;
  image: string;
  description: string;
  categoryId: number;
  brand: string;
  inStock: boolean;
  quantity: number;
  specifications?: {
    weight?: string;
    dimensions?: string;
    material?: string;
    color?: string;
    size?: string;
    model?: string;
    year?: number;
    compatibility?: string[];
  };
}

@Component({
  selector: 'app-admin-products',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './products.html',
  styleUrls: ['./products.css']
})
export class AdminProductsComponent implements OnInit, OnDestroy {
  products: Product[] = [];
  categories: ProductCategory[] = [];
  filteredProducts: Product[] = [];
  
  loadingState: LoadingState = {
    isLoading: true,
    error: undefined
  };

  // Form states
  showAddForm = false;
  showEditForm = false;
  showDeleteConfirm = false;
  selectedProduct: Product | null = null;

  productForm: ProductFormData = {
    name: '',
    price: 0,
    image: '',
    description: '',
    categoryId: 0,
    brand: '',
    inStock: true,
    quantity: 0
  };

  // Filters
  searchQuery = '';
  selectedCategoryId = 0;
  sortBy: 'name' | 'price' | 'createdAt' = 'name';
  sortOrder: 'asc' | 'desc' = 'asc';
  stockFilter: 'all' | 'inStock' | 'outOfStock' | 'lowStock' = 'all';

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  private subscriptions: Subscription[] = [];

  constructor(
    private productService: ProductService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadData(): void {
    this.loadingState.isLoading = true;

    // Load categories
    const categoriesSub = this.productService.getCategories().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.categories = response.data;
        }
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });

    // Load products
    const productsSub = this.productService.getAllProducts().subscribe({
      next: (response) => {
        this.loadingState.isLoading = false;
        
        if (response.success && response.data) {
          this.products = response.data;
          this.applyFilters();
        }
      },
      error: (error) => {
        console.error('Error loading products:', error);
        this.loadingState.isLoading = false;
        this.loadingState.error = 'Erreur lors du chargement des produits';
      }
    });

    this.subscriptions.push(categoriesSub, productsSub);
  }

  applyFilters(): void {
    let filtered = [...this.products];

    // Search filter
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(query) ||
        product.brand?.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query)
      );
    }

    // Category filter
    if (this.selectedCategoryId > 0) {
      filtered = filtered.filter(product => product.category.id === this.selectedCategoryId);
    }

    // Stock filter
    switch (this.stockFilter) {
      case 'inStock':
        filtered = filtered.filter(product => product.inStock && (product.quantity || 0) > 0);
        break;
      case 'outOfStock':
        filtered = filtered.filter(product => !product.inStock || (product.quantity || 0) === 0);
        break;
      case 'lowStock':
        filtered = filtered.filter(product => product.inStock && (product.quantity || 0) > 0 && (product.quantity || 0) < 10);
        break;
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (this.sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'createdAt':
          aValue = a.createdAt || new Date();
          bValue = b.createdAt || new Date();
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return this.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return this.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    this.filteredProducts = filtered;
    this.updatePagination();
  }

  private updatePagination(): void {
    this.totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
    if (this.currentPage > this.totalPages) {
      this.currentPage = 1;
    }
  }

  get paginatedProducts(): Product[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredProducts.slice(startIndex, endIndex);
  }

  get totalProducts(): number {
    return this.filteredProducts.length;
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onCategoryChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onStockFilterChange(): void {
    this.currentPage = 1;
    this.applyFilters();
  }

  onSortChange(): void {
    this.applyFilters();
  }

  onSortOrderToggle(): void {
    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    this.applyFilters();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  onAddProduct(): void {
    this.resetForm();
    this.showAddForm = true;
  }

  onEditProduct(product: Product): void {
    this.selectedProduct = product;
    this.productForm = {
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      description: product.description || '',
      categoryId: product.category.id,
      brand: product.brand || '',
      inStock: product.inStock,
      quantity: product.quantity || 0,
      specifications: product.specifications
    };
    this.showEditForm = true;
  }

  onDeleteProduct(product: Product): void {
    this.selectedProduct = product;
    this.showDeleteConfirm = true;
  }

  onViewProduct(product: Product): void {
    this.router.navigate(['/admin/products', product.id]);
  }

  onSubmitForm(): void {
    if (this.validateForm()) {
      if (this.showAddForm) {
        this.createProduct();
      } else if (this.showEditForm) {
        this.updateProduct();
      }
    }
  }

  private validateForm(): boolean {
    return this.productForm.name.trim() !== '' &&
           this.productForm.price > 0 &&
           this.productForm.categoryId > 0;
  }

  private createProduct(): void {
    // Mock create product
    const newProduct: Product = {
      id: Date.now(),
      name: this.productForm.name,
      price: this.productForm.price,
      image: this.productForm.image,
      description: this.productForm.description,
      category: this.categories.find(c => c.id === this.productForm.categoryId)!,
      brand: this.productForm.brand,
      inStock: this.productForm.inStock,
      quantity: this.productForm.quantity,
      specifications: this.productForm.specifications,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.products.push(newProduct);
    this.applyFilters();
    this.closeForm();
  }

  private updateProduct(): void {
    if (this.selectedProduct) {
      const index = this.products.findIndex(p => p.id === this.selectedProduct!.id);
      if (index !== -1) {
        this.products[index] = {
          ...this.products[index],
          name: this.productForm.name,
          price: this.productForm.price,
          image: this.productForm.image,
          description: this.productForm.description,
          category: this.categories.find(c => c.id === this.productForm.categoryId)!,
          brand: this.productForm.brand,
          inStock: this.productForm.inStock,
          quantity: this.productForm.quantity,
          specifications: this.productForm.specifications,
          updatedAt: new Date()
        };
        
        this.applyFilters();
        this.closeForm();
      }
    }
  }

  confirmDelete(): void {
    if (this.selectedProduct) {
      const index = this.products.findIndex(p => p.id === this.selectedProduct!.id);
      if (index !== -1) {
        this.products.splice(index, 1);
        this.applyFilters();
      }
    }
    this.closeDeleteConfirm();
  }

  private resetForm(): void {
    this.productForm = {
      name: '',
      price: 0,
      image: '',
      description: '',
      categoryId: 0,
      brand: '',
      inStock: true,
      quantity: 0
    };
    this.selectedProduct = null;
  }

  closeForm(): void {
    this.showAddForm = false;
    this.showEditForm = false;
    this.resetForm();
  }

  closeDeleteConfirm(): void {
    this.showDeleteConfirm = false;
    this.selectedProduct = null;
  }

  getStockStatus(product: Product): { text: string; class: string } {
    if (!product.inStock || (product.quantity || 0) === 0) {
      return { text: 'Rupture de stock', class: 'out-of-stock' };
    } else if ((product.quantity || 0) < 10) {
      return { text: 'Stock faible', class: 'low-stock' };
    } else {
      return { text: 'En stock', class: 'in-stock' };
    }
  }

  getCategoryName(categoryId: number): string {
    const category = this.categories.find(c => c.id === categoryId);
    return category ? category.name : 'Catégorie inconnue';
  }

  trackByProduct(index: number, product: Product): number {
    return product.id;
  }
}
