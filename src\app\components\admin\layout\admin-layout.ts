import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterOutlet, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { AuthService } from '../../../services/auth';

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  route: string;
  badge?: number;
  children?: MenuItem[];
}

interface UserInfo {
  name: string;
  email: string;
  role: string;
  avatar?: string;
}

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet],
  templateUrl: './admin-layout.html',
  styleUrls: ['./admin-layout.css']
})
export class AdminLayoutComponent implements OnInit, OnDestroy {
  isSidebarCollapsed = false;
  isMobileMenuOpen = false;
  currentRoute = '';
  
  userInfo: UserInfo = {
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'Administrateur',
    avatar: ''
  };

  menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Tableau de Bord',
      icon: '📊',
      route: '/admin/dashboard'
    },
    {
      id: 'orders',
      label: 'Commandes',
      icon: '📦',
      route: '/admin/orders',
      badge: 5
    },
    {
      id: 'products',
      label: 'Produits',
      icon: '🛍️',
      route: '/admin/products'
    },
    {
      id: 'categories',
      label: 'Catégories',
      icon: '📂',
      route: '/admin/categories'
    },
    {
      id: 'users',
      label: 'Utilisateurs',
      icon: '👥',
      route: '/admin/users'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: '📈',
      route: '/admin/analytics'
    },
    {
      id: 'settings',
      label: 'Paramètres',
      icon: '⚙️',
      route: '/admin/settings'
    }
  ];

  private subscriptions: Subscription[] = [];

  constructor(
    private router: Router,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.initializeLayout();
    this.subscribeToRouteChanges();
    this.loadUserInfo();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private initializeLayout(): void {
    // Set initial route
    this.currentRoute = this.router.url;
    
    // Check if sidebar should be collapsed on mobile
    this.checkMobileView();
    
    // Listen for window resize
    window.addEventListener('resize', () => this.checkMobileView());
  }

  private subscribeToRouteChanges(): void {
    const routerSub = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.currentRoute = event.url;
        this.closeMobileMenu();
      });

    this.subscriptions.push(routerSub);
  }

  private loadUserInfo(): void {
    // Mock user info - replace with actual service call
    const currentUser = this.authService.currentUser;
    if (currentUser) {
      this.userInfo = {
        name: `${currentUser.firstName} ${currentUser.lastName}`,
        email: currentUser.email,
        role: currentUser.role === 'admin' ? 'Administrateur' : 'Utilisateur',
        avatar: currentUser.avatar
      };
    }
  }

  private checkMobileView(): void {
    const isMobile = window.innerWidth < 768;
    if (isMobile) {
      this.isSidebarCollapsed = true;
    }
  }

  onToggleSidebar(): void {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }

  onToggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  onCloseMobileMenu(): void {
    this.isMobileMenuOpen = false;
  }

  private closeMobileMenu(): void {
    if (window.innerWidth < 768) {
      this.isMobileMenuOpen = false;
    }
  }

  onNavigate(route: string): void {
    this.router.navigate([route]);
    this.closeMobileMenu();
  }

  onLogout(): void {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
      this.authService.logout();
      this.router.navigate(['/login']);
    }
  }

  onGoToSite(): void {
    this.router.navigate(['/']);
  }

  isRouteActive(route: string): boolean {
    return this.currentRoute === route || this.currentRoute.startsWith(route + '/');
  }

  getPageTitle(): string {
    const activeItem = this.menuItems.find(item => this.isRouteActive(item.route));
    return activeItem ? activeItem.label : 'Administration';
  }

  getBreadcrumbs(): string[] {
    const segments = this.currentRoute.split('/').filter(segment => segment);
    const breadcrumbs: string[] = [];

    if (segments.length > 1) {
      // Remove 'admin' from segments
      const adminSegments = segments.slice(1);
      
      adminSegments.forEach(segment => {
        const menuItem = this.menuItems.find(item => 
          item.route.includes(segment)
        );
        
        if (menuItem) {
          breadcrumbs.push(menuItem.label);
        } else {
          // Capitalize first letter for unknown segments
          breadcrumbs.push(segment.charAt(0).toUpperCase() + segment.slice(1));
        }
      });
    }

    return breadcrumbs;
  }

  getUserInitials(): string {
    const names = this.userInfo.name.split(' ');
    return names.map(name => name.charAt(0)).join('').toUpperCase();
  }

  getNotificationCount(): number {
    // Mock notification count - replace with actual service
    return this.menuItems.reduce((total, item) => total + (item.badge || 0), 0);
  }

  onViewNotifications(): void {
    // Mock notification handler
    console.log('View notifications');
  }

  onViewProfile(): void {
    this.router.navigate(['/admin/profile']);
  }

  trackByMenuItem(index: number, item: MenuItem): string {
    return item.id;
  }
}
