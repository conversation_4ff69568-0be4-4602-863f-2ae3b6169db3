.admin-settings {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
}

.header-subtitle {
  margin: 0.5rem 0 0 0;
  color: #6c757d;
  font-size: 1rem;
}

.reset-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.reset-btn:hover:not(:disabled) {
  background: #545b62;
  transform: translateY(-2px);
}

.reset-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Tabs Navigation */
.tabs-navigation {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  overflow: hidden;
}

.tab-btn {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: white;
  color: #6c757d;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background: #f8f9fa;
}

/* Tab Content */
.tab-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-section {
  padding: 2rem;
}

.section-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.section-header h2 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.section-header p {
  margin: 0;
  color: #6c757d;
  font-size: 1rem;
}

/* Forms */
.settings-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-section h3 {
  margin: 0;
  color: #495057;
  font-size: 1.2rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.form-input, .form-select, .form-textarea {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: white;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #007bff;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-help {
  color: #6c757d;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

/* Checkboxes */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem 0;
}

.form-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.checkbox-text {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 2px solid #e9ecef;
}

.save-btn, .test-btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.save-btn {
  background: #28a745;
  color: white;
}

.test-btn {
  background: #17a2b8;
  color: white;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-2px);
}

.test-btn:hover:not(:disabled) {
  background: #138496;
  transform: translateY(-2px);
}

.save-btn:disabled, .test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading Spinner */
.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Payment Methods Grid */
.payment-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.payment-method {
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-method:hover {
  border-color: #007bff;
  background: #f8f9fa;
}

.payment-method.active {
  border-color: #007bff;
  background: #e3f2fd;
}

.payment-icon {
  font-size: 1.5rem;
}

.payment-info h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1rem;
}

.payment-info p {
  margin: 0.25rem 0 0 0;
  color: #6c757d;
  font-size: 0.8rem;
}

/* Security Indicators */
.security-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
}

.security-indicator.high {
  background: #d4edda;
  color: #155724;
}

.security-indicator.medium {
  background: #fff3cd;
  color: #856404;
}

.security-indicator.low {
  background: #f8d7da;
  color: #721c24;
}

/* Configuration Cards */
.config-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.config-card h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.config-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.active {
  background: #28a745;
}

.status-dot.inactive {
  background: #dc3545;
}

.status-text {
  font-weight: 600;
  font-size: 0.9rem;
}

.status-text.active {
  color: #28a745;
}

.status-text.inactive {
  color: #dc3545;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .admin-settings {
    padding: 1rem;
  }

  .settings-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .tabs-navigation {
    flex-direction: column;
  }

  .tab-btn {
    border-bottom: 1px solid #e9ecef;
    border-right: none;
  }

  .tab-btn.active {
    border-bottom-color: #e9ecef;
    border-left: 3px solid #007bff;
  }

  .settings-section {
    padding: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .save-btn, .test-btn {
    width: 100%;
    justify-content: center;
  }

  .checkbox-grid {
    grid-template-columns: 1fr;
  }

  .payment-methods {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .admin-settings {
    padding: 0.5rem;
  }

  .settings-section {
    padding: 1rem;
  }

  .section-header {
    margin-bottom: 1.5rem;
  }

  .form-section {
    gap: 1rem;
  }

  .form-grid {
    gap: 1rem;
  }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  .admin-settings {
    background: #1a1a1a;
    color: #e9ecef;
  }

  .settings-header,
  .tabs-navigation,
  .tab-content {
    background: #2d3748;
    color: #e9ecef;
  }

  .tab-btn {
    background: #2d3748;
    color: #a0aec0;
  }

  .tab-btn:hover {
    background: #4a5568;
    color: #e9ecef;
  }

  .tab-btn.active {
    background: #4a5568;
  }

  .form-input,
  .form-select,
  .form-textarea {
    background: #4a5568;
    border-color: #718096;
    color: #e9ecef;
  }

  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    border-color: #63b3ed;
  }

  .config-card {
    background: #4a5568;
    border-color: #718096;
  }
}
