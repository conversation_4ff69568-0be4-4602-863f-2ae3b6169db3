<div class="admin-analytics">
  <!-- Header -->
  <div class="analytics-header">
    <div class="header-content">
      <h1>Analytics & Rapports 📊</h1>
      <p class="header-subtitle">Analysez les performances de votre boutique</p>
    </div>
    <div class="header-actions">
      <button class="export-btn" (click)="onExportData()" [disabled]="loadingState.isLoading">
        📥 Exporter
      </button>
      <button class="refresh-btn" (click)="loadAnalytics()" [disabled]="loadingState.isLoading">
        🔄 Actualiser
      </button>
    </div>
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement des analytics...</p>
    </div>
  }

  @if (!loadingState.isLoading) {
    <!-- Date Range Filter -->
    <div class="filters-section">
      <div class="period-selector">
        <label>Période:</label>
        <select [(ngModel)]="selectedPeriod" (change)="onPeriodChange()" class="period-select">
          <option value="week">Cette semaine</option>
          <option value="month">Ce mois</option>
          <option value="quarter">Ce trimestre</option>
          <option value="year">Cette année</option>
          <option value="custom">Personnalisée</option>
        </select>
      </div>

      @if (selectedPeriod === 'custom') {
        <div class="date-range">
          <div class="date-input-group">
            <label>Du:</label>
            <input 
              type="date" 
              [(ngModel)]="dateRange.startDate" 
              (change)="onDateRangeChange()"
              class="date-input">
          </div>
          <div class="date-input-group">
            <label>Au:</label>
            <input 
              type="date" 
              [(ngModel)]="dateRange.endDate" 
              (change)="onDateRangeChange()"
              class="date-input">
          </div>
        </div>
      }
    </div>

    <!-- Key Metrics -->
    <div class="metrics-grid">
      <div class="metric-card revenue">
        <div class="metric-icon">💰</div>
        <div class="metric-content">
          <h3>{{ formatCurrency(analyticsData.totalRevenue) }}</h3>
          <p>Chiffre d'Affaires</p>
          <div class="metric-trend positive">+12.5% vs période précédente</div>
        </div>
      </div>

      <div class="metric-card orders">
        <div class="metric-icon">📦</div>
        <div class="metric-content">
          <h3>{{ analyticsData.totalOrders }}</h3>
          <p>Commandes Totales</p>
          <div class="metric-trend positive">+8.3% vs période précédente</div>
        </div>
      </div>

      <div class="metric-card average">
        <div class="metric-icon">📈</div>
        <div class="metric-content">
          <h3>{{ formatCurrency(analyticsData.averageOrderValue) }}</h3>
          <p>Panier Moyen</p>
          <div class="metric-trend positive">+3.7% vs période précédente</div>
        </div>
      </div>

      <div class="metric-card conversion">
        <div class="metric-icon">🎯</div>
        <div class="metric-content">
          <h3>{{ formatPercentage(analyticsData.conversionRate) }}</h3>
          <p>Taux de Conversion</p>
          <div class="metric-trend neutral">-0.2% vs période précédente</div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
      <!-- Sales Chart -->
      <div class="chart-card">
        <div class="chart-header">
          <h2>Évolution des Ventes</h2>
          <div class="chart-legend">
            <span class="legend-item revenue">
              <span class="legend-color"></span>
              Chiffre d'Affaires
            </span>
            <span class="legend-item orders">
              <span class="legend-color"></span>
              Commandes
            </span>
          </div>
        </div>
        <div class="chart-container">
          <div class="chart-bars">
            @for (data of analyticsData.salesByMonth; track trackByMonth($index, data)) {
              <div class="chart-month">
                <div class="bars-container">
                  <div 
                    class="revenue-bar" 
                    [style.height.%]="(data.revenue / getMaxRevenueForChart()) * 100"
                    [title]="formatCurrency(data.revenue)">
                  </div>
                  <div 
                    class="orders-bar" 
                    [style.height.%]="(data.orders / getMaxOrdersForChart()) * 100"
                    [title]="data.orders + ' commandes'">
                  </div>
                </div>
                <div class="month-label">{{ data.month }}</div>
              </div>
            }
          </div>
        </div>
      </div>

      <!-- Orders by Status -->
      <div class="chart-card">
        <div class="chart-header">
          <h2>Répartition des Commandes</h2>
        </div>
        <div class="chart-container">
          <div class="pie-chart">
            @for (status of analyticsData.ordersByStatus; track trackByStatus($index, status)) {
              <div class="pie-segment">
                <div 
                  class="segment-bar"
                  [style.width.%]="status.percentage"
                  [style.background-color]="getStatusColor(status.status)">
                </div>
                <div class="segment-info">
                  <span class="segment-label">{{ getStatusText(status.status) }}</span>
                  <span class="segment-value">{{ status.count }} ({{ formatPercentage(status.percentage) }})</span>
                </div>
              </div>
            }
          </div>
        </div>
      </div>
    </div>

    <!-- Data Tables Section -->
    <div class="tables-section">
      <!-- Top Products -->
      <div class="table-card">
        <div class="table-header">
          <h2>Produits les Plus Vendus</h2>
          <button class="view-all-btn" (click)="router.navigate(['/admin/products'])">
            Voir tous
          </button>
        </div>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>Produit</th>
                <th>Catégorie</th>
                <th>Vendus</th>
                <th>Chiffre d'Affaires</th>
              </tr>
            </thead>
            <tbody>
              @for (product of analyticsData.topProducts; track trackByProduct($index, product)) {
                <tr class="table-row" (click)="onViewProductDetails(product)">
                  <td class="product-cell">
                    <strong>{{ product.name }}</strong>
                  </td>
                  <td class="category-cell">{{ product.category }}</td>
                  <td class="quantity-cell">{{ product.totalSold }}</td>
                  <td class="revenue-cell">
                    <strong>{{ formatCurrency(product.revenue) }}</strong>
                  </td>
                </tr>
              }
            </tbody>
          </table>
        </div>
      </div>

      <!-- Revenue by Category -->
      <div class="table-card">
        <div class="table-header">
          <h2>Chiffre d'Affaires par Catégorie</h2>
        </div>
        <div class="table-container">
          <div class="category-list">
            @for (category of analyticsData.revenueByCategory; track trackByCategory($index, category)) {
              <div class="category-item">
                <div class="category-info">
                  <span class="category-name">{{ category.category }}</span>
                  <span class="category-percentage">{{ formatPercentage(category.percentage) }}</span>
                </div>
                <div class="category-bar-container">
                  <div 
                    class="category-bar"
                    [style.width.%]="category.percentage">
                  </div>
                </div>
                <div class="category-revenue">
                  {{ formatCurrency(category.revenue) }}
                </div>
              </div>
            }
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    <div class="summary-section">
      <div class="summary-card">
        <h3>Résumé de Performance</h3>
        <div class="summary-stats">
          <div class="summary-item">
            <span class="summary-label">Meilleur mois:</span>
            <span class="summary-value">Octobre ({{ formatCurrency(45750.50) }})</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Produit star:</span>
            <span class="summary-value">Casque Shark Speed-R</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Catégorie leader:</span>
            <span class="summary-value">Casques (33.2%)</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Taux de livraison:</span>
            <span class="summary-value">96.8%</span>
          </div>
        </div>
      </div>

      <div class="summary-card">
        <h3>Recommandations</h3>
        <div class="recommendations">
          <div class="recommendation-item">
            <span class="recommendation-icon">💡</span>
            <span class="recommendation-text">Augmenter le stock des casques Shark</span>
          </div>
          <div class="recommendation-item">
            <span class="recommendation-icon">📈</span>
            <span class="recommendation-text">Promouvoir la catégorie Pantalons</span>
          </div>
          <div class="recommendation-item">
            <span class="recommendation-icon">🎯</span>
            <span class="recommendation-text">Optimiser le taux de conversion</span>
          </div>
          <div class="recommendation-item">
            <span class="recommendation-icon">🚀</span>
            <span class="recommendation-text">Lancer une campagne pour les gants</span>
          </div>
        </div>
      </div>
    </div>
  }
</div>
