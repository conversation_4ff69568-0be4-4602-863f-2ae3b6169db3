<div class="home-container">
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-content">
      <div class="hero-text">
        <h1>Bienvenue chez <span class="brand">MotoShop</span> 🏍️</h1>
        <p class="hero-subtitle">
          Votre destination ultime pour tous vos besoins moto.
          Découvrez notre collection exclusive de motos, casques, et accessoires de qualité premium.
        </p>
        <div class="hero-actions">
          <button class="cta-primary" (click)="onShopNow()">
            Découvrir nos produits
          </button>
          <button class="cta-secondary" (click)="onLoginClick()">
            Se connecter
          </button>
        </div>
      </div>
      <div class="hero-image">
        <div class="hero-bike">🏍️</div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="stats-section">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-number">{{ stats.totalProducts }}+</div>
        <div class="stat-label">Produits</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.happyCustomers }}+</div>
        <div class="stat-label">Clients satisfaits</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.yearsExperience }}+</div>
        <div class="stat-label">Années d'expérience</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.deliveryTime }}h</div>
        <div class="stat-label">Livraison rapide</div>
      </div>
    </div>
  </section>

  <!-- Categories Section -->
  <section class="categories-section">
    <div class="section-header">
      <h2>Nos Catégories</h2>
      <p>Explorez notre gamme complète de produits moto</p>
    </div>

    @if (loadingState.isLoading) {
      <div class="loading-grid">
        @for (item of [1,2,3,4]; track item) {
          <div class="category-skeleton"></div>
        }
      </div>
    } @else if (categories.length > 0) {
      <div class="categories-grid">
        @for (category of categories; track trackByCategory($index, category)) {
          <div class="category-card" (click)="onCategoryClick(category)">
            <div class="category-icon">🏍️</div>
            <h3>{{ category.name }}</h3>
            <p>{{ category.description }}</p>
            <div class="category-count">Voir produits</div>
          </div>
        }
      </div>
    } @else {
      <div class="empty-state">
        <div class="empty-icon">📦</div>
        <h3>Aucune catégorie disponible</h3>
        <p>Les catégories seront bientôt disponibles</p>
      </div>
    }
  </section>

  <!-- Featured Products Section -->
  <section class="featured-section">
    <div class="section-header">
      <h2>Produits Vedettes</h2>
      <p>Découvrez nos produits les plus populaires</p>
    </div>

    @if (loadingState.isLoading) {
      <div class="products-loading">
        @for (item of [1,2,3]; track item) {
          <div class="product-skeleton"></div>
        }
      </div>
    } @else if (featuredProducts.length > 0) {
      <div class="featured-grid">
        @for (product of featuredProducts; track trackByProduct($index, product)) {
          <div class="featured-card" (click)="onProductClick(product)">
            <div class="product-image">
              @if (product.image) {
                <img [src]="product.image" [alt]="product.name" />
              } @else {
                <div class="placeholder-image">🏍️</div>
              }
            </div>
            <div class="product-info">
              <h3>{{ product.name }}</h3>
              <p class="product-brand">{{ product.brand || 'MotoShop' }}</p>
              <div class="product-price">
                <span class="current-price">{{ product.price }} TND</span>
              </div>
              <div class="product-rating">
                @for (star of [1,2,3,4,5]; track star) {
                  <span class="star" [class.filled]="star <= (product.rating || 0)">⭐</span>
                }
                <span class="rating-text">({{ product.reviews?.length || 0 }})</span>
              </div>
            </div>
          </div>
        }
      </div>
    } @else {
      <div class="empty-state">
        <div class="empty-icon">🛍️</div>
        <h3>Aucun produit vedette</h3>
        <p>Les produits vedettes seront bientôt disponibles</p>
      </div>
    }
  </section>

  <!-- Call to Action Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2>Prêt à commencer votre aventure moto ?</h2>
      <p>Rejoignez des milliers de motards qui nous font confiance</p>
      <div class="cta-buttons">
        <button class="cta-primary" (click)="onShopNow()">
          Voir tous les produits
        </button>
        <button class="cta-outline" (click)="onRegisterClick()">
          Créer un compte
        </button>
      </div>
    </div>
  </section>
</div>
