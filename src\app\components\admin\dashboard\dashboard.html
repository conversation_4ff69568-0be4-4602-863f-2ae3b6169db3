<div class="admin-dashboard">
  <!-- Header -->
  <div class="dashboard-header">
    <div class="header-content">
      <h1>Tableau de Bord Admin 📊</h1>
      <p class="header-subtitle">Vue d'ensemble de votre boutique MotoShop</p>
    </div>
    <div class="header-actions">
      <button class="refresh-btn" (click)="loadDashboardData()" [disabled]="loadingState.isLoading">
        🔄 Actualiser
      </button>
    </div>
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement du tableau de bord...</p>
    </div>
  }

  <!-- Error State -->
  @if (loadingState.error) {
    <div class="error-container">
      <div class="error-icon">⚠️</div>
      <h3>Erreur de chargement</h3>
      <p>{{ loadingState.error }}</p>
      <button class="retry-btn" (click)="loadDashboardData()">
        Réessayer
      </button>
    </div>
  }

  <!-- Dashboard Content -->
  @if (!loadingState.isLoading && !loadingState.error) {
    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card revenue">
        <div class="stat-icon">💰</div>
        <div class="stat-content">
          <h3>{{ stats.totalRevenue | number:'1.0-0' }} TND</h3>
          <p>Chiffre d'affaires</p>
        </div>
        <div class="stat-trend positive">+12.5%</div>
      </div>

      <div class="stat-card orders">
        <div class="stat-icon">📦</div>
        <div class="stat-content">
          <h3>{{ stats.totalOrders }}</h3>
          <p>Total Commandes</p>
        </div>
        <div class="stat-trend positive">+8.2%</div>
      </div>

      <div class="stat-card products">
        <div class="stat-icon">🛍️</div>
        <div class="stat-content">
          <h3>{{ stats.totalProducts }}</h3>
          <p>Produits</p>
        </div>
        <div class="stat-trend neutral">{{ stats.lowStockProducts }} en rupture</div>
      </div>

      <div class="stat-card users">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <h3>{{ stats.totalUsers }}</h3>
          <p>Utilisateurs</p>
        </div>
        <div class="stat-trend positive">+{{ stats.newUsersThisMonth }} ce mois</div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2>Actions Rapides</h2>
      <div class="actions-grid">
        <button class="action-card" (click)="onNavigateToProducts()">
          <div class="action-icon">➕</div>
          <h3>Ajouter Produit</h3>
          <p>Créer un nouveau produit</p>
        </button>

        <button class="action-card" (click)="onNavigateToOrders()">
          <div class="action-icon">📋</div>
          <h3>Gérer Commandes</h3>
          <p>{{ stats.pendingOrders }} en attente</p>
        </button>

        <button class="action-card" (click)="onNavigateToUsers()">
          <div class="action-icon">👤</div>
          <h3>Gérer Utilisateurs</h3>
          <p>Voir tous les clients</p>
        </button>

        <button class="action-card" (click)="onNavigateToAnalytics()">
          <div class="action-icon">📈</div>
          <h3>Analytics</h3>
          <p>Rapports détaillés</p>
        </button>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="main-content">
      <!-- Recent Orders -->
      <div class="content-card">
        <div class="card-header">
          <h2>Commandes Récentes</h2>
          <button class="view-all-btn" (click)="onNavigateToOrders()">
            Voir tout
          </button>
        </div>
        <div class="card-content">
          @if (recentOrders.length > 0) {
            <div class="orders-list">
              @for (order of recentOrders; track trackByOrder($index, order)) {
                <div class="order-item" (click)="onViewOrder(order.id)">
                  <div class="order-info">
                    <h4>#{{ order.orderNumber }}</h4>
                    <p class="order-customer">{{ order.itemCount }} article(s)</p>
                    <p class="order-date">{{ order.createdAt | date:'short':'fr' }}</p>
                  </div>
                  <div class="order-status">
                    <span 
                      class="status-badge" 
                      [style.background-color]="getStatusColor(order.status)">
                      {{ order.status }}
                    </span>
                  </div>
                  <div class="order-amount">
                    <strong>{{ order.total }} {{ order.currency }}</strong>
                  </div>
                </div>
              }
            </div>
          } @else {
            <div class="empty-state">
              <div class="empty-icon">📦</div>
              <p>Aucune commande récente</p>
            </div>
          }
        </div>
      </div>

      <!-- Recent Activities -->
      <div class="content-card">
        <div class="card-header">
          <h2>Activités Récentes</h2>
        </div>
        <div class="card-content">
          @if (recentActivities.length > 0) {
            <div class="activities-list">
              @for (activity of recentActivities; track trackByActivity($index, activity)) {
                <div class="activity-item" [class]="activity.status">
                  <div class="activity-icon">
                    {{ getActivityIcon(activity.type) }}
                  </div>
                  <div class="activity-content">
                    <p class="activity-message">{{ activity.message }}</p>
                    <span class="activity-time">{{ formatTimeAgo(activity.timestamp) }}</span>
                  </div>
                </div>
              }
            </div>
          } @else {
            <div class="empty-state">
              <div class="empty-icon">📋</div>
              <p>Aucune activité récente</p>
            </div>
          }
        </div>
      </div>

      <!-- Top Products -->
      <div class="content-card">
        <div class="card-header">
          <h2>Produits Populaires</h2>
          <button class="view-all-btn" (click)="onNavigateToProducts()">
            Voir tout
          </button>
        </div>
        <div class="card-content">
          @if (topProducts.length > 0) {
            <div class="products-list">
              @for (product of topProducts; track trackByProduct($index, product)) {
                <div class="product-item" (click)="onViewProduct(product.id)">
                  <div class="product-image">
                    @if (product.image) {
                      <img [src]="product.image" [alt]="product.name" />
                    } @else {
                      <div class="placeholder-image">🏍️</div>
                    }
                  </div>
                  <div class="product-info">
                    <h4>{{ product.name }}</h4>
                    <p class="product-brand">{{ product.brand || 'MotoShop' }}</p>
                    <p class="product-price">{{ product.price }} TND</p>
                  </div>
                  <div class="product-stock">
                    <span 
                      class="stock-badge"
                      [class.low-stock]="(product.quantity || 0) < 10">
                      {{ product.quantity || 0 }} en stock
                    </span>
                  </div>
                </div>
              }
            </div>
          } @else {
            <div class="empty-state">
              <div class="empty-icon">🛍️</div>
              <p>Aucun produit disponible</p>
            </div>
          }
        </div>
      </div>

      <!-- Charts Section -->
      <div class="content-card full-width">
        <div class="card-header">
          <h2>Aperçu des Ventes</h2>
        </div>
        <div class="card-content">
          <div class="charts-grid">
            <div class="chart-container">
              <h3>Ventes des 7 derniers jours</h3>
              <div class="chart-placeholder">
                <div class="chart-bars">
                  @for (value of salesChartData.datasets[0]?.data || []; track $index) {
                    <div 
                      class="chart-bar" 
                      [style.height.%]="(value / 5000) * 100">
                      <span class="bar-value">{{ value }}</span>
                    </div>
                  }
                </div>
                <div class="chart-labels">
                  @for (label of salesChartData.labels; track $index) {
                    <span class="chart-label">{{ label }}</span>
                  }
                </div>
              </div>
            </div>

            <div class="chart-container">
              <h3>Répartition des Commandes</h3>
              <div class="pie-chart-placeholder">
                @for (data of ordersChartData.datasets[0]?.data || []; track $index) {
                  <div class="pie-segment">
                    <div 
                      class="segment-color" 
                      [style.background-color]="ordersChartData.datasets[0]?.backgroundColor?.[$index]">
                    </div>
                    <span class="segment-label">
                      {{ ordersChartData.labels[$index] }}: {{ data }}
                    </span>
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  }
</div>
