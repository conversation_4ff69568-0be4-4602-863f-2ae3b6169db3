<div class="admin-categories">
  <!-- Header -->
  <div class="categories-header">
    <div class="header-content">
      <h1>Gestion des Catégories 📂</h1>
      <p class="header-subtitle">Organisez vos produits par catégories</p>
    </div>
    <div class="header-actions">
      <button class="add-btn" (click)="onAddCategory()">
        ➕ Nouvelle Catégorie
      </button>
      <button class="refresh-btn" (click)="loadCategories()" [disabled]="loadingState.isLoading">
        🔄 Actualiser
      </button>
    </div>
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement des catégories...</p>
    </div>
  }

  @if (!loadingState.isLoading) {
    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card total">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <h3>{{ categoryStats.totalCategories }}</h3>
          <p>Total Catégories</p>
        </div>
      </div>

      <div class="stat-card active">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <h3>{{ categoryStats.activeCategories }}</h3>
          <p>Actives</p>
        </div>
      </div>

      <div class="stat-card inactive">
        <div class="stat-icon">⏸️</div>
        <div class="stat-content">
          <h3>{{ categoryStats.inactiveCategories }}</h3>
          <p>Inactives</p>
        </div>
      </div>

      <div class="stat-card parent">
        <div class="stat-icon">🏷️</div>
        <div class="stat-content">
          <h3>{{ categoryStats.parentCategories }}</h3>
          <p>Principales</p>
        </div>
      </div>

      <div class="stat-card sub">
        <div class="stat-icon">🔗</div>
        <div class="stat-content">
          <h3>{{ categoryStats.subCategories }}</h3>
          <p>Sous-catégories</p>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="filters-section">
      <div class="search-container">
        <input 
          type="text" 
          placeholder="Rechercher une catégorie..." 
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          class="search-input">
        <span class="search-icon">🔍</span>
      </div>

      <div class="filter-controls">
        <select 
          [(ngModel)]="statusFilter" 
          (change)="onStatusFilterChange()"
          class="status-filter">
          <option value="all">Tous les statuts</option>
          <option value="active">Actives</option>
          <option value="inactive">Inactives</option>
        </select>

        <div class="sort-controls">
          <label>Trier par:</label>
          <button 
            class="sort-btn"
            [class.active]="sortBy === 'name'"
            (click)="onSortChange('name')">
            Nom {{ sortBy === 'name' ? (sortOrder === 'asc' ? '↑' : '↓') : '' }}
          </button>
          <button 
            class="sort-btn"
            [class.active]="sortBy === 'sortOrder'"
            (click)="onSortChange('sortOrder')">
            Ordre {{ sortBy === 'sortOrder' ? (sortOrder === 'asc' ? '↑' : '↓') : '' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Categories Table -->
    <div class="categories-table-container">
      <table class="categories-table">
        <thead>
          <tr>
            <th>Image</th>
            <th>Nom</th>
            <th>Slug</th>
            <th>Parent</th>
            <th>Ordre</th>
            <th>Statut</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @if (filteredCategories.length > 0) {
            @for (category of filteredCategories; track trackByCategory($index, category)) {
              <tr class="category-row">
                <td class="image-cell">
                  @if (category.image) {
                    <img [src]="category.image" [alt]="category.name" class="category-image" />
                  } @else {
                    <div class="placeholder-image">📂</div>
                  }
                </td>
                <td class="name-cell">
                  <div class="category-name">{{ category.name }}</div>
                  @if (category.description) {
                    <div class="category-description">{{ category.description }}</div>
                  }
                </td>
                <td class="slug-cell">
                  <code>{{ category.slug }}</code>
                </td>
                <td class="parent-cell">
                  {{ getParentCategoryName(category.parentId) }}
                </td>
                <td class="order-cell">
                  {{ category.sortOrder || 0 }}
                </td>
                <td class="status-cell">
                  <span 
                    class="status-badge"
                    [class]="getCategoryStatusClass(category)">
                    {{ getCategoryStatusText(category) }}
                  </span>
                </td>
                <td class="actions-cell">
                  <div class="action-buttons">
                    <button 
                      class="action-btn edit"
                      (click)="onEditCategory(category)"
                      title="Modifier">
                      ✏️
                    </button>
                    <button 
                      class="action-btn toggle"
                      [class]="(category.isActive !== false) ? 'deactivate' : 'activate'"
                      (click)="onToggleCategoryStatus(category)"
                      [title]="(category.isActive !== false) ? 'Désactiver' : 'Activer'">
                      {{ (category.isActive !== false) ? '⏸️' : '▶️' }}
                    </button>
                    <button 
                      class="action-btn delete"
                      (click)="onDeleteCategory(category)"
                      title="Supprimer">
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            }
          } @else {
            <tr>
              <td colspan="7" class="empty-state">
                <div class="empty-content">
                  <div class="empty-icon">📂</div>
                  <p>Aucune catégorie trouvée</p>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  }
</div>

<!-- Add/Edit Category Modal -->
@if (showAddForm || showEditForm) {
  <div class="modal-overlay" (click)="closeForm()">
    <div class="modal-content category-form-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>{{ showAddForm ? 'Nouvelle Catégorie' : 'Modifier la Catégorie' }}</h2>
        <button class="close-btn" (click)="closeForm()">✕</button>
      </div>
      
      <div class="modal-body">
        <form class="category-form" (ngSubmit)="onSubmitForm()">
          <div class="form-grid">
            <div class="form-group">
              <label for="categoryName">Nom *</label>
              <input 
                id="categoryName" 
                type="text" 
                [(ngModel)]="categoryForm.name" 
                name="categoryName"
                (input)="onNameChange()"
                class="form-input"
                required>
            </div>

            <div class="form-group">
              <label for="categorySlug">Slug *</label>
              <input 
                id="categorySlug" 
                type="text" 
                [(ngModel)]="categoryForm.slug" 
                name="categorySlug"
                class="form-input"
                required>
            </div>

            <div class="form-group full-width">
              <label for="categoryDescription">Description</label>
              <textarea 
                id="categoryDescription" 
                [(ngModel)]="categoryForm.description" 
                name="categoryDescription"
                class="form-textarea"
                rows="3"></textarea>
            </div>

            <div class="form-group">
              <label for="categoryImage">URL de l'Image</label>
              <input 
                id="categoryImage" 
                type="url" 
                [(ngModel)]="categoryForm.image" 
                name="categoryImage"
                class="form-input"
                placeholder="https://...">
            </div>

            <div class="form-group">
              <label for="categoryParent">Catégorie Parent</label>
              <select 
                id="categoryParent" 
                [(ngModel)]="categoryForm.parentId" 
                name="categoryParent"
                class="form-select">
                <option [value]="undefined">Catégorie principale</option>
                @for (parent of getParentCategories(); track parent.id) {
                  @if (!selectedCategory || parent.id !== selectedCategory.id) {
                    <option [value]="parent.id">{{ parent.name }}</option>
                  }
                }
              </select>
            </div>

            <div class="form-group">
              <label for="categorySortOrder">Ordre de Tri</label>
              <input 
                id="categorySortOrder" 
                type="number" 
                [(ngModel)]="categoryForm.sortOrder" 
                name="categorySortOrder"
                class="form-input"
                min="0">
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="categoryForm.isActive" 
                  name="categoryIsActive"
                  class="form-checkbox">
                <span class="checkbox-text">Catégorie active</span>
              </label>
            </div>

            <div class="form-group">
              <label for="categoryMetaTitle">Meta Titre (SEO)</label>
              <input 
                id="categoryMetaTitle" 
                type="text" 
                [(ngModel)]="categoryForm.metaTitle" 
                name="categoryMetaTitle"
                class="form-input"
                maxlength="60">
            </div>

            <div class="form-group full-width">
              <label for="categoryMetaDescription">Meta Description (SEO)</label>
              <textarea 
                id="categoryMetaDescription" 
                [(ngModel)]="categoryForm.metaDescription" 
                name="categoryMetaDescription"
                class="form-textarea"
                rows="2"
                maxlength="160"></textarea>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="cancel-btn" (click)="closeForm()">Annuler</button>
            <button type="submit" class="save-btn" [disabled]="loadingState.isLoading">
              {{ showAddForm ? 'Créer' : 'Sauvegarder' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
}

<!-- Delete Confirmation Modal -->
@if (showDeleteConfirm && selectedCategory) {
  <div class="modal-overlay" (click)="closeDeleteConfirm()">
    <div class="modal-content delete-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>Confirmer la Suppression</h2>
        <button class="close-btn" (click)="closeDeleteConfirm()">✕</button>
      </div>
      
      <div class="modal-body">
        <div class="delete-warning">
          <div class="warning-icon">⚠️</div>
          <p>Êtes-vous sûr de vouloir supprimer la catégorie <strong>{{ selectedCategory.name }}</strong> ?</p>
          <p class="warning-text">Cette action est irréversible et peut affecter les produits associés.</p>
        </div>
        
        <div class="modal-actions">
          <button class="cancel-btn" (click)="closeDeleteConfirm()">Annuler</button>
          <button class="delete-btn" (click)="confirmDelete()">Supprimer</button>
        </div>
      </div>
    </div>
  </div>
}
