// Product interfaces for e-commerce moto site
export interface Product {
  id: number;
  name: string;
  price: number;
  image: string;
  description?: string;
  category: ProductCategory;
  brand?: string;
  inStock: boolean;
  quantity?: number;
  specifications?: ProductSpecifications;
  rating?: number;
  reviews?: Review[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ProductCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: number;
  isActive?: boolean;
  sortOrder?: number;
  metaTitle?: string;
  metaDescription?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ProductSpecifications {
  weight?: string;
  dimensions?: string;
  material?: string;
  color?: string;
  size?: string;
  model?: string;
  year?: number;
  compatibility?: string[];
}

export interface Review {
  id: number;
  userId: number;
  userName: string;
  rating: number;
  comment: string;
  createdAt: Date;
  verified?: boolean;
}

export interface ProductFilter {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  brand?: string;
  inStock?: boolean;
  rating?: number;
  sortBy?: 'price' | 'name' | 'rating' | 'newest';
  sortOrder?: 'asc' | 'desc';
}

export interface ProductSearchResult {
  products: Product[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  filters: ProductFilter;
}
