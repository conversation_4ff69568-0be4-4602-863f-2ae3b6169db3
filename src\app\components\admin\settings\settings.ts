import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { LoadingState } from '../../../interfaces';

interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  currency: string;
  language: string;
  timezone: string;
  maintenanceMode: boolean;
}

interface PaymentSettings {
  enableCreditCard: boolean;
  enablePaypal: boolean;
  enableBankTransfer: boolean;
  enableCashOnDelivery: boolean;
  paypalClientId: string;
  stripePublicKey: string;
  bankAccountDetails: string;
}

interface ShippingSettings {
  freeShippingThreshold: number;
  standardShippingCost: number;
  expressShippingCost: number;
  internationalShippingCost: number;
  enableInternationalShipping: boolean;
  processingDays: number;
  standardDeliveryDays: number;
  expressDeliveryDays: number;
}

interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
  enableOrderConfirmation: boolean;
  enableShippingNotification: boolean;
  enableNewsletters: boolean;
}

interface SecuritySettings {
  enableTwoFactor: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
  passwordMinLength: number;
  requirePasswordComplexity: boolean;
  enableCaptcha: boolean;
}

@Component({
  selector: 'app-admin-settings',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './settings.html',
  styleUrls: ['./settings.css']
})
export class AdminSettingsComponent implements OnInit, OnDestroy {
  activeTab: 'site' | 'payment' | 'shipping' | 'email' | 'security' = 'site';

  loadingState: LoadingState = {
    isLoading: false,
    error: undefined
  };

  siteSettings: SiteSettings = {
    siteName: 'MotoShop Tunisia',
    siteDescription: 'Votre boutique spécialisée en équipements moto',
    siteUrl: 'https://motoshop.tn',
    contactEmail: '<EMAIL>',
    contactPhone: '+216 71 123 456',
    address: '123 Avenue Habib Bourguiba, Tunis 1000, Tunisie',
    currency: 'TND',
    language: 'fr',
    timezone: 'Africa/Tunis',
    maintenanceMode: false
  };

  paymentSettings: PaymentSettings = {
    enableCreditCard: true,
    enablePaypal: true,
    enableBankTransfer: true,
    enableCashOnDelivery: true,
    paypalClientId: '',
    stripePublicKey: '',
    bankAccountDetails: 'IBAN: TN59 1234 5678 9012 3456 7890'
  };

  shippingSettings: ShippingSettings = {
    freeShippingThreshold: 500,
    standardShippingCost: 15,
    expressShippingCost: 25,
    internationalShippingCost: 50,
    enableInternationalShipping: false,
    processingDays: 2,
    standardDeliveryDays: 5,
    expressDeliveryDays: 2
  };

  emailSettings: EmailSettings = {
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    fromEmail: '<EMAIL>',
    fromName: 'MotoShop Tunisia',
    enableOrderConfirmation: true,
    enableShippingNotification: true,
    enableNewsletters: false
  };

  securitySettings: SecuritySettings = {
    enableTwoFactor: false,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    requirePasswordComplexity: true,
    enableCaptcha: false
  };

  private subscriptions: Subscription[] = [];

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.loadSettings();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadSettings(): void {
    // Mock loading settings - replace with actual service calls
    this.loadingState.isLoading = true;
    
    setTimeout(() => {
      this.loadingState.isLoading = false;
    }, 500);
  }

  onTabChange(tab: 'site' | 'payment' | 'shipping' | 'email' | 'security'): void {
    this.activeTab = tab;
  }

  onSaveSiteSettings(): void {
    this.loadingState.isLoading = true;
    
    // Mock save operation
    setTimeout(() => {
      this.loadingState.isLoading = false;
      this.showSuccessMessage('Paramètres du site sauvegardés avec succès');
    }, 1000);
  }

  onSavePaymentSettings(): void {
    this.loadingState.isLoading = true;
    
    // Mock save operation
    setTimeout(() => {
      this.loadingState.isLoading = false;
      this.showSuccessMessage('Paramètres de paiement sauvegardés avec succès');
    }, 1000);
  }

  onSaveShippingSettings(): void {
    this.loadingState.isLoading = true;
    
    // Mock save operation
    setTimeout(() => {
      this.loadingState.isLoading = false;
      this.showSuccessMessage('Paramètres de livraison sauvegardés avec succès');
    }, 1000);
  }

  onSaveEmailSettings(): void {
    this.loadingState.isLoading = true;
    
    // Mock save operation
    setTimeout(() => {
      this.loadingState.isLoading = false;
      this.showSuccessMessage('Paramètres email sauvegardés avec succès');
    }, 1000);
  }

  onSaveSecuritySettings(): void {
    this.loadingState.isLoading = true;
    
    // Mock save operation
    setTimeout(() => {
      this.loadingState.isLoading = false;
      this.showSuccessMessage('Paramètres de sécurité sauvegardés avec succès');
    }, 1000);
  }

  onTestEmailSettings(): void {
    this.loadingState.isLoading = true;
    
    // Mock test email
    setTimeout(() => {
      this.loadingState.isLoading = false;
      this.showSuccessMessage('Email de test envoyé avec succès');
    }, 2000);
  }

  onResetToDefaults(): void {
    if (confirm('Êtes-vous sûr de vouloir restaurer les paramètres par défaut ?')) {
      this.loadingState.isLoading = true;
      
      setTimeout(() => {
        // Reset current tab settings to defaults
        switch (this.activeTab) {
          case 'site':
            this.resetSiteSettings();
            break;
          case 'payment':
            this.resetPaymentSettings();
            break;
          case 'shipping':
            this.resetShippingSettings();
            break;
          case 'email':
            this.resetEmailSettings();
            break;
          case 'security':
            this.resetSecuritySettings();
            break;
        }
        
        this.loadingState.isLoading = false;
        this.showSuccessMessage('Paramètres restaurés aux valeurs par défaut');
      }, 1000);
    }
  }

  private resetSiteSettings(): void {
    this.siteSettings = {
      siteName: 'MotoShop Tunisia',
      siteDescription: 'Votre boutique spécialisée en équipements moto',
      siteUrl: 'https://motoshop.tn',
      contactEmail: '<EMAIL>',
      contactPhone: '+216 71 123 456',
      address: '123 Avenue Habib Bourguiba, Tunis 1000, Tunisie',
      currency: 'TND',
      language: 'fr',
      timezone: 'Africa/Tunis',
      maintenanceMode: false
    };
  }

  private resetPaymentSettings(): void {
    this.paymentSettings = {
      enableCreditCard: true,
      enablePaypal: false,
      enableBankTransfer: true,
      enableCashOnDelivery: true,
      paypalClientId: '',
      stripePublicKey: '',
      bankAccountDetails: ''
    };
  }

  private resetShippingSettings(): void {
    this.shippingSettings = {
      freeShippingThreshold: 500,
      standardShippingCost: 15,
      expressShippingCost: 25,
      internationalShippingCost: 50,
      enableInternationalShipping: false,
      processingDays: 2,
      standardDeliveryDays: 5,
      expressDeliveryDays: 2
    };
  }

  private resetEmailSettings(): void {
    this.emailSettings = {
      smtpHost: '',
      smtpPort: 587,
      smtpUsername: '',
      smtpPassword: '',
      fromEmail: '',
      fromName: 'MotoShop Tunisia',
      enableOrderConfirmation: true,
      enableShippingNotification: true,
      enableNewsletters: false
    };
  }

  private resetSecuritySettings(): void {
    this.securitySettings = {
      enableTwoFactor: false,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      requirePasswordComplexity: true,
      enableCaptcha: false
    };
  }

  private showSuccessMessage(message: string): void {
    // Mock success message - replace with actual notification service
    alert(message);
  }

  getTabIcon(tab: string): string {
    const icons = {
      site: '🏪',
      payment: '💳',
      shipping: '🚚',
      email: '📧',
      security: '🔒'
    };
    return icons[tab as keyof typeof icons] || '⚙️';
  }

  getTabTitle(tab: string): string {
    const titles = {
      site: 'Site Web',
      payment: 'Paiements',
      shipping: 'Livraison',
      email: 'Email',
      security: 'Sécurité'
    };
    return titles[tab as keyof typeof titles] || 'Paramètres';
  }
}
