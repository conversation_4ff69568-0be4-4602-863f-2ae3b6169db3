import { Injectable } from '@angular/core';
import { 
  CanActivate, 
  CanActivateChild, 
  ActivatedRouteSnapshot, 
  RouterStateSnapshot, 
  Router 
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate, CanActivateChild {
  
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAdminAccess(state.url);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkAdminAccess(state.url);
  }

  private checkAdminAccess(url: string): Observable<boolean> {
    return this.authService.currentUser$.pipe(
      map(user => {
        if (!user) {
          // User not authenticated
          this.redirectToLogin(url);
          return false;
        }

        if (!this.isAdmin(user)) {
          // User authenticated but not admin
          this.redirectToUnauthorized();
          return false;
        }

        // User is authenticated and is admin
        return true;
      }),
      catchError(error => {
        console.error('Error checking admin access:', error);
        this.redirectToLogin(url);
        return of(false);
      })
    );
  }

  private isAdmin(user: any): boolean {
    // Check if user has admin role
    return user && (
      user.role === 'admin' || 
      user.role === 'administrator' ||
      user.isAdmin === true ||
      (user.roles && user.roles.includes('admin'))
    );
  }

  private redirectToLogin(returnUrl: string): void {
    // Store the attempted URL for redirecting after login
    localStorage.setItem('returnUrl', returnUrl);
    
    // Navigate to login page
    this.router.navigate(['/login'], {
      queryParams: { returnUrl: returnUrl }
    });
  }

  private redirectToUnauthorized(): void {
    // Navigate to unauthorized page or home page
    this.router.navigate(['/'], {
      queryParams: { error: 'unauthorized' }
    });
    
    // Optionally show a message
    console.warn('Access denied: Admin privileges required');
  }
}
