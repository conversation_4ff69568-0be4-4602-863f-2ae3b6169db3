<div class="admin-products">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>Gestion des Produits 🛍️</h1>
      <p class="header-subtitle">Gérez votre catalogue de produits MotoShop</p>
    </div>
    <div class="header-actions">
      <button class="add-btn" (click)="onAddProduct()">
        ➕ Ajouter Produit
      </button>
    </div>
  </div>

  <!-- Loading State -->
  @if (loadingState.isLoading) {
    <div class="loading-container">
      <div class="loading-spinner"></div>
      <p>Chargement des produits...</p>
    </div>
  }

  <!-- Error State -->
  @if (loadingState.error) {
    <div class="error-container">
      <div class="error-icon">⚠️</div>
      <h3>Erreur de chargement</h3>
      <p>{{ loadingState.error }}</p>
      <button class="retry-btn" (click)="loadData()">
        Réessayer
      </button>
    </div>
  }

  <!-- Main Content -->
  @if (!loadingState.isLoading && !loadingState.error) {
    <!-- Filters -->
    <div class="filters-section">
      <div class="filters-row">
        <!-- Search -->
        <div class="filter-group">
          <label>Rechercher</label>
          <input 
            type="text" 
            [(ngModel)]="searchQuery"
            (input)="onSearchChange()"
            placeholder="Nom, marque, description..."
            class="search-input">
        </div>

        <!-- Category Filter -->
        <div class="filter-group">
          <label>Catégorie</label>
          <select 
            [(ngModel)]="selectedCategoryId"
            (change)="onCategoryChange()"
            class="filter-select">
            <option value="0">Toutes les catégories</option>
            @for (category of categories; track category.id) {
              <option [value]="category.id">{{ category.name }}</option>
            }
          </select>
        </div>

        <!-- Stock Filter -->
        <div class="filter-group">
          <label>Stock</label>
          <select 
            [(ngModel)]="stockFilter"
            (change)="onStockFilterChange()"
            class="filter-select">
            <option value="all">Tous</option>
            <option value="inStock">En stock</option>
            <option value="lowStock">Stock faible</option>
            <option value="outOfStock">Rupture</option>
          </select>
        </div>

        <!-- Sort -->
        <div class="filter-group">
          <label>Trier par</label>
          <div class="sort-controls">
            <select 
              [(ngModel)]="sortBy"
              (change)="onSortChange()"
              class="filter-select">
              <option value="name">Nom</option>
              <option value="price">Prix</option>
              <option value="createdAt">Date</option>
            </select>
            <button 
              class="sort-order-btn"
              (click)="onSortOrderToggle()"
              [class.desc]="sortOrder === 'desc'">
              {{ sortOrder === 'asc' ? '↑' : '↓' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Results Info -->
      <div class="results-info">
        <span class="results-count">{{ totalProducts }} produit(s) trouvé(s)</span>
      </div>
    </div>

    <!-- Products Table -->
    <div class="products-table-container">
      <table class="products-table">
        <thead>
          <tr>
            <th>Image</th>
            <th>Nom</th>
            <th>Catégorie</th>
            <th>Prix</th>
            <th>Stock</th>
            <th>Statut</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @if (paginatedProducts.length > 0) {
            @for (product of paginatedProducts; track trackByProduct($index, product)) {
              <tr class="product-row">
                <td class="product-image-cell">
                  @if (product.image) {
                    <img [src]="product.image" [alt]="product.name" class="product-image" />
                  } @else {
                    <div class="placeholder-image">🏍️</div>
                  }
                </td>
                <td class="product-name-cell">
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-brand">{{ product.brand || 'MotoShop' }}</div>
                </td>
                <td>{{ product.category.name }}</td>
                <td class="price-cell">{{ product.price }} TND</td>
                <td class="stock-cell">{{ product.quantity || 0 }}</td>
                <td class="status-cell">
                  <span 
                    class="status-badge"
                    [class]="getStockStatus(product).class">
                    {{ getStockStatus(product).text }}
                  </span>
                </td>
                <td class="actions-cell">
                  <div class="action-buttons">
                    <button 
                      class="action-btn view"
                      (click)="onViewProduct(product)"
                      title="Voir détails">
                      👁️
                    </button>
                    <button 
                      class="action-btn edit"
                      (click)="onEditProduct(product)"
                      title="Modifier">
                      ✏️
                    </button>
                    <button 
                      class="action-btn delete"
                      (click)="onDeleteProduct(product)"
                      title="Supprimer">
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            }
          } @else {
            <tr>
              <td colspan="7" class="empty-state">
                <div class="empty-content">
                  <div class="empty-icon">📦</div>
                  <h3>Aucun produit trouvé</h3>
                  <p>Aucun produit ne correspond à vos critères de recherche</p>
                </div>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    @if (totalPages > 1) {
      <div class="pagination">
        <button 
          class="page-btn"
          [disabled]="currentPage === 1"
          (click)="onPageChange(currentPage - 1)">
          ← Précédent
        </button>
        
        @for (page of [].constructor(totalPages); track $index) {
          <button 
            class="page-btn"
            [class.active]="currentPage === $index + 1"
            (click)="onPageChange($index + 1)">
            {{ $index + 1 }}
          </button>
        }
        
        <button 
          class="page-btn"
          [disabled]="currentPage === totalPages"
          (click)="onPageChange(currentPage + 1)">
          Suivant →
        </button>
      </div>
    }
  }

  <!-- Add/Edit Product Modal -->
  @if (showAddForm || showEditForm) {
    <div class="modal-overlay" (click)="closeForm()">
      <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h2>{{ showAddForm ? 'Ajouter un Produit' : 'Modifier le Produit' }}</h2>
          <button class="close-btn" (click)="closeForm()">✕</button>
        </div>
        
        <form (ngSubmit)="onSubmitForm()" class="product-form">
          <div class="form-row">
            <div class="form-group">
              <label for="name">Nom du produit *</label>
              <input 
                type="text" 
                id="name"
                [(ngModel)]="productForm.name"
                name="name"
                required
                class="form-input">
            </div>
            
            <div class="form-group">
              <label for="brand">Marque</label>
              <input 
                type="text" 
                id="brand"
                [(ngModel)]="productForm.brand"
                name="brand"
                class="form-input">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="price">Prix (TND) *</label>
              <input 
                type="number" 
                id="price"
                [(ngModel)]="productForm.price"
                name="price"
                min="0"
                step="0.01"
                required
                class="form-input">
            </div>
            
            <div class="form-group">
              <label for="category">Catégorie *</label>
              <select 
                id="category"
                [(ngModel)]="productForm.categoryId"
                name="category"
                required
                class="form-select">
                <option value="0">Sélectionner une catégorie</option>
                @for (category of categories; track category.id) {
                  <option [value]="category.id">{{ category.name }}</option>
                }
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="quantity">Quantité</label>
              <input 
                type="number" 
                id="quantity"
                [(ngModel)]="productForm.quantity"
                name="quantity"
                min="0"
                class="form-input">
            </div>
            
            <div class="form-group checkbox-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  [(ngModel)]="productForm.inStock"
                  name="inStock">
                <span class="checkmark"></span>
                En stock
              </label>
            </div>
          </div>

          <div class="form-group">
            <label for="image">URL de l'image</label>
            <input 
              type="url" 
              id="image"
              [(ngModel)]="productForm.image"
              name="image"
              class="form-input">
          </div>

          <div class="form-group">
            <label for="description">Description</label>
            <textarea 
              id="description"
              [(ngModel)]="productForm.description"
              name="description"
              rows="4"
              class="form-textarea"></textarea>
          </div>

          <div class="form-actions">
            <button type="button" class="cancel-btn" (click)="closeForm()">
              Annuler
            </button>
            <button type="submit" class="submit-btn">
              {{ showAddForm ? 'Ajouter' : 'Modifier' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  }

  <!-- Delete Confirmation Modal -->
  @if (showDeleteConfirm && selectedProduct) {
    <div class="modal-overlay" (click)="closeDeleteConfirm()">
      <div class="modal-content small" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h2>Confirmer la suppression</h2>
          <button class="close-btn" (click)="closeDeleteConfirm()">✕</button>
        </div>
        
        <div class="modal-body">
          <p>Êtes-vous sûr de vouloir supprimer le produit :</p>
          <strong>{{ selectedProduct.name }}</strong>
          <p class="warning-text">Cette action est irréversible.</p>
        </div>

        <div class="modal-actions">
          <button class="cancel-btn" (click)="closeDeleteConfirm()">
            Annuler
          </button>
          <button class="delete-btn" (click)="confirmDelete()">
            Supprimer
          </button>
        </div>
      </div>
    </div>
  }
</div>
