import { Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { ProductsComponent } from './components/user/products/products';
import { ProductDetailsComponent } from './components/user/product-details/product-details';
import { LoginComponent } from './components/user/login/login';
import { RegisterComponent } from './components/user/register/register';
import { CartComponent } from './components/user/cart/cart';
import { CheckoutComponent } from './components/user/checkout/checkout';
import { OrdersComponent } from './components/user/orders/orders';
import { ProfileComponent } from './components/user/profile/profile';

// Admin Components
import { AdminLayoutComponent } from './components/admin/layout/admin-layout';
import { AdminDashboardComponent } from './components/admin/dashboard/dashboard';
import { AdminProductsComponent } from './components/admin/products/products';
import { AdminOrdersComponent } from './components/admin/orders/orders';
import { AdminUsersComponent } from './components/admin/users/users';
import { AdminAnalyticsComponent } from './components/admin/analytics/analytics';
import { AdminSettingsComponent } from './components/admin/settings/settings';
import { AdminCategoriesComponent } from './components/admin/categories/categories';

// Guards
import { AdminGuard } from './guards/admin.guard';

export const routes: Routes = [
  // Public routes
  { path: '', component: HomeComponent },
  { path: 'products', component: ProductsComponent },
  { path: 'product/:id', component: ProductDetailsComponent },
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },

  // User routes
  { path: 'cart', component: CartComponent },
  { path: 'checkout', component: CheckoutComponent },
  { path: 'orders', component: OrdersComponent },
  { path: 'profile', component: ProfileComponent },

  // Admin routes with layout and guard
  {
    path: 'admin',
    component: AdminLayoutComponent,
    canActivate: [AdminGuard],
    canActivateChild: [AdminGuard],
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: AdminDashboardComponent },
      { path: 'products', component: AdminProductsComponent },
      { path: 'orders', component: AdminOrdersComponent },
      { path: 'users', component: AdminUsersComponent },
      { path: 'categories', component: AdminCategoriesComponent },
      { path: 'analytics', component: AdminAnalyticsComponent },
      { path: 'settings', component: AdminSettingsComponent }
    ]
  },

  // Wildcard route
  { path: '**', redirectTo: '', pathMatch: 'full' }
];
