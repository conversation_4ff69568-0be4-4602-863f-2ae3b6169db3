import { Routes } from '@angular/router';
import { HomeComponent } from './components/home/<USER>';
import { ProductsComponent } from './components/user/products/products';
import { ProductDetailsComponent } from './components/user/product-details/product-details';
import { LoginComponent } from './components/user/login/login';
import { RegisterComponent } from './components/user/register/register';
import { CartComponent } from './components/user/cart/cart';
import { CheckoutComponent } from './components/user/checkout/checkout';
import { OrdersComponent } from './components/user/orders/orders';
import { ProfileComponent } from './components/user/profile/profile';

// Admin Components
import { AdminDashboardComponent } from './components/admin/dashboard/dashboard';
import { AdminProductsComponent } from './components/admin/products/products';

export const routes: Routes = [
  // Public routes
  { path: '', component: HomeComponent },
  { path: 'products', component: ProductsComponent },
  { path: 'product/:id', component: ProductDetailsComponent },
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },

  // User routes
  { path: 'cart', component: CartComponent },
  { path: 'checkout', component: CheckoutComponent },
  { path: 'orders', component: OrdersComponent },
  { path: 'profile', component: ProfileComponent },

  // Admin routes
  { path: 'admin', redirectTo: 'admin/dashboard', pathMatch: 'full' },
  { path: 'admin/dashboard', component: AdminDashboardComponent },
  { path: 'admin/products', component: AdminProductsComponent },

  // Wildcard route
  { path: '**', redirectTo: '', pathMatch: 'full' }
];
