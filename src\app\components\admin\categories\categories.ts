import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { ProductService } from '../../../services/product';
import { 
  ProductCategory, 
  LoadingState,
  ApiResponse 
} from '../../../interfaces';

interface CategoryFormData {
  id?: number;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: number;
  isActive: boolean;
  sortOrder: number;
  metaTitle?: string;
  metaDescription?: string;
}

interface CategoryStats {
  totalCategories: number;
  activeCategories: number;
  inactiveCategories: number;
  parentCategories: number;
  subCategories: number;
}

@Component({
  selector: 'app-admin-categories',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './categories.html',
  styleUrls: ['./categories.css']
})
export class AdminCategoriesComponent implements OnInit, OnDestroy {
  categories: ProductCategory[] = [];
  filteredCategories: ProductCategory[] = [];
  selectedCategory: ProductCategory | null = null;
  
  loadingState: LoadingState = {
    isLoading: true,
    error: undefined
  };

  categoryStats: CategoryStats = {
    totalCategories: 0,
    activeCategories: 0,
    inactiveCategories: 0,
    parentCategories: 0,
    subCategories: 0
  };

  // Form states
  showAddForm = false;
  showEditForm = false;
  showDeleteConfirm = false;

  categoryForm: CategoryFormData = {
    name: '',
    slug: '',
    description: '',
    image: '',
    isActive: true,
    sortOrder: 0,
    metaTitle: '',
    metaDescription: ''
  };

  // Filters
  searchTerm = '';
  statusFilter: 'all' | 'active' | 'inactive' = 'all';
  sortBy: 'name' | 'sortOrder' | 'createdAt' = 'sortOrder';
  sortOrder: 'asc' | 'desc' = 'asc';

  private subscriptions: Subscription[] = [];

  constructor(
    private productService: ProductService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCategories();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  loadCategories(): void {
    this.loadingState.isLoading = true;
    
    const categoriesSub = this.productService.getCategories().subscribe({
      next: (response: ApiResponse<ProductCategory[]>) => {
        if (response.success && response.data) {
          this.categories = response.data;
          this.calculateStats();
          this.applyFilters();
        }
        this.loadingState.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading categories:', error);
        this.loadingState.isLoading = false;
        this.loadingState.error = 'Erreur lors du chargement des catégories';
      }
    });

    this.subscriptions.push(categoriesSub);
  }

  private calculateStats(): void {
    this.categoryStats = {
      totalCategories: this.categories.length,
      activeCategories: this.categories.filter(c => c.isActive !== false).length,
      inactiveCategories: this.categories.filter(c => c.isActive === false).length,
      parentCategories: this.categories.filter(c => !c.parentId).length,
      subCategories: this.categories.filter(c => c.parentId).length
    };
  }

  applyFilters(): void {
    let filtered = [...this.categories];

    // Search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(category => 
        category.name.toLowerCase().includes(term) ||
        category.slug.toLowerCase().includes(term) ||
        (category.description && category.description.toLowerCase().includes(term))
      );
    }

    // Status filter
    if (this.statusFilter !== 'all') {
      const isActive = this.statusFilter === 'active';
      filtered = filtered.filter(category => (category.isActive !== false) === isActive);
    }

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (this.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'sortOrder':
          comparison = (a.sortOrder || 0) - (b.sortOrder || 0);
          break;
        case 'createdAt':
          const aDate = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          const bDate = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          comparison = aDate - bDate;
          break;
        default:
          comparison = a.id - b.id;
      }

      return this.sortOrder === 'desc' ? -comparison : comparison;
    });

    this.filteredCategories = filtered;
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onStatusFilterChange(): void {
    this.applyFilters();
  }

  onSortChange(sortBy: 'name' | 'sortOrder' | 'createdAt'): void {
    if (this.sortBy === sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = sortBy;
      this.sortOrder = 'asc';
    }
    this.applyFilters();
  }

  onAddCategory(): void {
    this.resetForm();
    this.showAddForm = true;
  }

  onEditCategory(category: ProductCategory): void {
    this.selectedCategory = category;
    this.categoryForm = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      image: category.image || '',
      parentId: category.parentId,
      isActive: category.isActive !== false,
      sortOrder: category.sortOrder || 0,
      metaTitle: category.metaTitle || '',
      metaDescription: category.metaDescription || ''
    };
    this.showEditForm = true;
  }

  onDeleteCategory(category: ProductCategory): void {
    this.selectedCategory = category;
    this.showDeleteConfirm = true;
  }

  onToggleCategoryStatus(category: ProductCategory): void {
    const categoryIndex = this.categories.findIndex(c => c.id === category.id);
    if (categoryIndex !== -1) {
      this.categories[categoryIndex].isActive = !(this.categories[categoryIndex].isActive !== false);
      this.calculateStats();
      this.applyFilters();
    }
  }

  onSubmitForm(): void {
    if (this.validateForm()) {
      if (this.showAddForm) {
        this.createCategory();
      } else if (this.showEditForm) {
        this.updateCategory();
      }
    }
  }

  private validateForm(): boolean {
    return this.categoryForm.name.trim() !== '' && 
           this.categoryForm.slug.trim() !== '';
  }

  private createCategory(): void {
    const newCategory: ProductCategory = {
      id: Date.now(), // Mock ID
      name: this.categoryForm.name,
      slug: this.categoryForm.slug,
      description: this.categoryForm.description,
      image: this.categoryForm.image,
      parentId: this.categoryForm.parentId,
      isActive: this.categoryForm.isActive,
      sortOrder: this.categoryForm.sortOrder,
      metaTitle: this.categoryForm.metaTitle,
      metaDescription: this.categoryForm.metaDescription,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.categories.push(newCategory);
    this.calculateStats();
    this.applyFilters();
    this.closeForm();
  }

  private updateCategory(): void {
    if (this.selectedCategory) {
      const categoryIndex = this.categories.findIndex(c => c.id === this.selectedCategory!.id);
      if (categoryIndex !== -1) {
        this.categories[categoryIndex] = {
          ...this.categories[categoryIndex],
          name: this.categoryForm.name,
          slug: this.categoryForm.slug,
          description: this.categoryForm.description,
          image: this.categoryForm.image,
          parentId: this.categoryForm.parentId,
          isActive: this.categoryForm.isActive,
          sortOrder: this.categoryForm.sortOrder,
          metaTitle: this.categoryForm.metaTitle,
          metaDescription: this.categoryForm.metaDescription,
          updatedAt: new Date()
        };
        this.calculateStats();
        this.applyFilters();
      }
    }
    this.closeForm();
  }

  confirmDelete(): void {
    if (this.selectedCategory) {
      const index = this.categories.findIndex(c => c.id === this.selectedCategory!.id);
      if (index !== -1) {
        this.categories.splice(index, 1);
        this.calculateStats();
        this.applyFilters();
      }
    }
    this.closeDeleteConfirm();
  }

  private resetForm(): void {
    this.categoryForm = {
      name: '',
      slug: '',
      description: '',
      image: '',
      isActive: true,
      sortOrder: 0,
      metaTitle: '',
      metaDescription: ''
    };
    this.selectedCategory = null;
  }

  closeForm(): void {
    this.showAddForm = false;
    this.showEditForm = false;
    this.resetForm();
  }

  closeDeleteConfirm(): void {
    this.showDeleteConfirm = false;
    this.selectedCategory = null;
  }

  onNameChange(): void {
    // Auto-generate slug from name
    if (this.categoryForm.name && !this.categoryForm.slug) {
      this.categoryForm.slug = this.generateSlug(this.categoryForm.name);
    }
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[àáâãäå]/g, 'a')
      .replace(/[èéêë]/g, 'e')
      .replace(/[ìíîï]/g, 'i')
      .replace(/[òóôõö]/g, 'o')
      .replace(/[ùúûü]/g, 'u')
      .replace(/[ç]/g, 'c')
      .replace(/[ñ]/g, 'n')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  getParentCategories(): ProductCategory[] {
    return this.categories.filter(c => !c.parentId);
  }

  getCategoryStatusText(category: ProductCategory): string {
    return (category.isActive !== false) ? 'Actif' : 'Inactif';
  }

  getCategoryStatusClass(category: ProductCategory): string {
    return (category.isActive !== false) ? 'active' : 'inactive';
  }

  getParentCategoryName(parentId?: number): string {
    if (!parentId) return 'Catégorie principale';
    const parent = this.categories.find(c => c.id === parentId);
    return parent ? parent.name : 'Catégorie inconnue';
  }

  trackByCategory(index: number, category: ProductCategory): number {
    return category.id;
  }
}
