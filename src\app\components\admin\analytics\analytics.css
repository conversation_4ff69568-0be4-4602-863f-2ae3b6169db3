.admin-analytics {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
}

.header-subtitle {
  margin: 0.5rem 0 0 0;
  color: #6c757d;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.export-btn, .refresh-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.export-btn {
  background: #28a745;
  color: white;
}

.refresh-btn {
  background: #007bff;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-2px);
}

.refresh-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
}

.export-btn:disabled, .refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Filters */
.filters-section {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  gap: 2rem;
  align-items: center;
  flex-wrap: wrap;
}

.period-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.period-selector label {
  font-weight: 600;
  color: #495057;
}

.period-select {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
}

.date-range {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.date-input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-input-group label {
  font-weight: 600;
  color: #495057;
}

.date-input {
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-icon {
  font-size: 2.5rem;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f8f9fa;
}

.metric-content h3 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

.metric-content p {
  margin: 0.25rem 0 0.5rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.metric-trend {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.metric-trend.positive {
  background: #d4edda;
  color: #155724;
}

.metric-trend.negative {
  background: #f8d7da;
  color: #721c24;
}

.metric-trend.neutral {
  background: #fff3cd;
  color: #856404;
}

.metric-card.revenue .metric-icon { background: #e8f5e8; }
.metric-card.orders .metric-icon { background: #e3f2fd; }
.metric-card.average .metric-icon { background: #fff3e0; }
.metric-card.conversion .metric-icon { background: #f3e5f5; }

/* Charts Section */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.chart-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  padding: 1.5rem;
  border-bottom: 2px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.chart-legend {
  display: flex;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6c757d;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-item.revenue .legend-color {
  background: #007bff;
}

.legend-item.orders .legend-color {
  background: #28a745;
}

.chart-container {
  padding: 1.5rem;
  height: 300px;
}

.chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 250px;
  gap: 0.5rem;
}

.chart-month {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bars-container {
  flex: 1;
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 2px;
  width: 100%;
}

.revenue-bar, .orders-bar {
  width: 12px;
  min-height: 4px;
  border-radius: 2px 2px 0 0;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.revenue-bar {
  background: #007bff;
}

.orders-bar {
  background: #28a745;
}

.revenue-bar:hover, .orders-bar:hover {
  opacity: 0.8;
}

.month-label {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: 600;
}

/* Pie Chart */
.pie-chart {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pie-segment {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.segment-bar {
  height: 20px;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.segment-bar:hover {
  opacity: 0.8;
}

.segment-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.segment-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.segment-value {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Tables Section */
.tables-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.table-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  padding: 1.5rem;
  border-bottom: 2px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.view-all-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  background: #0056b3;
}

.table-container {
  max-height: 400px;
  overflow-y: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
}

.data-table td {
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.table-row {
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.product-cell strong {
  color: #2c3e50;
}

.category-cell {
  color: #6c757d;
}

.quantity-cell {
  font-weight: 600;
  color: #495057;
}

.revenue-cell strong {
  color: #28a745;
}

/* Category List */
.category-list {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.category-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-weight: 600;
  color: #2c3e50;
}

.category-percentage {
  color: #6c757d;
  font-size: 0.9rem;
}

.category-bar-container {
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
}

.category-bar {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #28a745);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.category-revenue {
  text-align: right;
  font-weight: 600;
  color: #28a745;
  font-size: 0.9rem;
}

/* Summary Section */
.summary-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.summary-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.summary-card h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.summary-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  color: #6c757d;
  font-weight: 500;
}

.summary-value {
  color: #2c3e50;
  font-weight: 600;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.recommendation-icon {
  font-size: 1.2rem;
}

.recommendation-text {
  color: #495057;
  font-weight: 500;
}

/* Responsive */
@media (max-width: 1200px) {
  .charts-section {
    grid-template-columns: 1fr;
  }

  .tables-section {
    grid-template-columns: 1fr;
  }

  .summary-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .admin-analytics {
    padding: 1rem;
  }

  .analytics-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range {
    flex-direction: column;
    align-items: stretch;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .chart-bars {
    gap: 0.25rem;
  }

  .bars-container {
    gap: 1px;
  }

  .revenue-bar, .orders-bar {
    width: 8px;
  }

  .data-table {
    font-size: 0.9rem;
  }

  .data-table th, .data-table td {
    padding: 0.75rem 0.5rem;
  }
}
